/**
 * Design System Tokens
 * 
 * This file defines the core design tokens for the Multiprints application.
 * These tokens ensure consistency across the entire application.
 */

export const colors = {
  // Brand colors
  brand: {
    primary: {
      50: '#EBF5FF',
      100: '#D6EBFF',
      200: '#ADD6FF',
      300: '#85C2FF',
      400: '#5CADFF',
      500: '#3399FF', // Primary brand color
      600: '#0A7AE6',
      700: '#0066CC',
      800: '#0052A3',
      900: '#003D7A',
    },
    secondary: {
      50: '#F2F0FF',
      100: '#E4E0FF',
      200: '#C9C1FF',
      300: '#AEA3FF',
      400: '#9384FF',
      500: '#7866FF', // Secondary brand color
      600: '#5A48E6',
      700: '#4A3ACC',
      800: '#3B2EA3',
      900: '#2C227A',
    },
    accent: {
      50: '#FFF0F5',
      100: '#FFE0EB',
      200: '#FFC1D6',
      300: '#FFA3C2',
      400: '#FF84AD',
      500: '#FF6699', // Accent color
      600: '#E64880',
      700: '#CC3366',
      800: '#A3294F',
      900: '#7A1F3D',
    },
  },
  
  // Semantic colors
  semantic: {
    success: {
      50: '#EAFAF0',
      100: '#D5F5E0',
      200: '#ABEBB2',
      300: '#82E285',
      400: '#58D857',
      500: '#2ECF2A', // Success color
      600: '#25B924',
      700: '#1CA31C',
      800: '#168C16',
      900: '#0F660F',
    },
    warning: {
      50: '#FFF9EB',
      100: '#FFF3D6',
      200: '#FFE7AD',
      300: '#FFDB85',
      400: '#FFCF5C',
      500: '#FFC333', // Warning color
      600: '#E6AB25',
      700: '#CC941C',
      800: '#A37716',
      900: '#7A5A10',
    },
    error: {
      50: '#FEEBEB',
      100: '#FDD6D6',
      200: '#FBADAD',
      300: '#F98585',
      400: '#F75C5C',
      500: '#F53333', // Error color
      600: '#DC2525',
      700: '#C21C1C',
      800: '#A31616',
      900: '#7A1010',
    },
    info: {
      50: '#EBF7FF',
      100: '#D6EFFF',
      200: '#ADDEFF',
      300: '#85CEFF',
      400: '#5CBEFF',
      500: '#33ADFF', // Info color
      600: '#2599E6',
      700: '#1C85CC',
      800: '#1670A3',
      900: '#10547A',
    },
  },
  
  // Neutral colors
  neutral: {
    white: '#FFFFFF',
    black: '#000000',
    gray: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280',
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827',
      950: '#0D1117',
    },
  },
};

export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem', // 2px
  1: '0.25rem',    // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem',     // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem',    // 12px
  3.5: '0.875rem', // 14px
  4: '1rem',       // 16px
  5: '1.25rem',    // 20px
  6: '1.5rem',     // 24px
  7: '1.75rem',    // 28px
  8: '2rem',       // 32px
  9: '2.25rem',    // 36px
  10: '2.5rem',    // 40px
  11: '2.75rem',   // 44px
  12: '3rem',      // 48px
  14: '3.5rem',    // 56px
  16: '4rem',      // 64px
  20: '5rem',      // 80px
  24: '6rem',      // 96px
  28: '7rem',      // 112px
  32: '8rem',      // 128px
  36: '9rem',      // 144px
  40: '10rem',     // 160px
  44: '11rem',     // 176px
  48: '12rem',     // 192px
  52: '13rem',     // 208px
  56: '14rem',     // 224px
  60: '15rem',     // 240px
  64: '16rem',     // 256px
  72: '18rem',     // 288px
  80: '20rem',     // 320px
  96: '24rem',     // 384px
};

export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    serif: ['Georgia', 'serif'],
    mono: ['JetBrains Mono', 'monospace'],
  },
  fontSize: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
    '7xl': '4.5rem',   // 72px
    '8xl': '6rem',     // 96px
    '9xl': '8rem',     // 128px
  },
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
};

export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
  // Custom shadows
  'card-hover': '0 22px 40px rgba(0, 0, 0, 0.1)',
  'card-active': '0 15px 30px rgba(0, 0, 0, 0.15)',
  'dropdown': '0 4px 20px rgba(0, 0, 0, 0.15)',
};

export const borderRadius = {
  none: '0',
  sm: '0.125rem',    // 2px
  DEFAULT: '0.25rem', // 4px
  md: '0.375rem',    // 6px
  lg: '0.5rem',      // 8px
  xl: '0.75rem',     // 12px
  '2xl': '1rem',     // 16px
  '3xl': '1.5rem',   // 24px
  full: '9999px',
};

export const transitions = {
  duration: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },
  timing: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
};

export const zIndex = {
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  auto: 'auto',
  // Custom z-indices
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modalBackdrop: '1040',
  modal: '1050',
  popover: '1060',
  tooltip: '1070',
};

export const breakpoints = {
  xs: '480px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// Export all tokens as a single object
export const tokens = {
  colors,
  spacing,
  typography,
  shadows,
  borderRadius,
  transitions,
  zIndex,
  breakpoints,
};

export default tokens;