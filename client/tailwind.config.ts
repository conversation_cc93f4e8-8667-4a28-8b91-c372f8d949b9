import type { Config } from 'tailwindcss'
import { tokens } from './lib/design-system/tokens'

const config: Config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    screens: {
      xs: tokens.breakpoints.xs,
      sm: tokens.breakpoints.sm,
      md: tokens.breakpoints.md,
      lg: tokens.breakpoints.lg,
      xl: tokens.breakpoints.xl,
      '2xl': tokens.breakpoints['2xl'],
    },
    colors: {
      transparent: 'transparent',
      current: 'currentColor',
      white: tokens.colors.neutral.white,
      black: tokens.colors.neutral.black,
      
      // Brand colors
      primary: {
        DEFAULT: tokens.colors.brand.primary[500],
        ...tokens.colors.brand.primary,
        foreground: tokens.colors.neutral.white,
      },
      secondary: {
        DEFAULT: tokens.colors.brand.secondary[500],
        ...tokens.colors.brand.secondary,
        foreground: tokens.colors.neutral.white,
      },
      accent: {
        DEFAULT: tokens.colors.brand.accent[500],
        ...tokens.colors.brand.accent,
        foreground: tokens.colors.neutral.white,
      },
      
      // Semantic colors
      success: {
        DEFAULT: tokens.colors.semantic.success[500],
        ...tokens.colors.semantic.success,
        foreground: tokens.colors.neutral.white,
      },
      warning: {
        DEFAULT: tokens.colors.semantic.warning[500],
        ...tokens.colors.semantic.warning,
        foreground: tokens.colors.neutral.white,
      },
      error: {
        DEFAULT: tokens.colors.semantic.error[500],
        ...tokens.colors.semantic.error,
        foreground: tokens.colors.neutral.white,
      },
      info: {
        DEFAULT: tokens.colors.semantic.info[500],
        ...tokens.colors.semantic.info,
        foreground: tokens.colors.neutral.white,
      },
      
      // Neutral colors (gray)
      gray: tokens.colors.neutral.gray,
      
      // UI semantic colors
      background: {
        DEFAULT: tokens.colors.neutral.white,
        dark: tokens.colors.neutral.gray[950],
      },
      foreground: {
        DEFAULT: tokens.colors.neutral.gray[900],
        dark: tokens.colors.neutral.gray[100],
      },
      muted: {
        DEFAULT: tokens.colors.neutral.gray[100],
        foreground: tokens.colors.neutral.gray[500],
        dark: tokens.colors.neutral.gray[800],
        'dark-foreground': tokens.colors.neutral.gray[400],
      },
      border: {
        DEFAULT: tokens.colors.neutral.gray[200],
        dark: tokens.colors.neutral.gray[800],
      },
      ring: {
        DEFAULT: tokens.colors.brand.primary[300],
      },
      input: {
        DEFAULT: tokens.colors.neutral.gray[200],
        dark: tokens.colors.neutral.gray[800],
      },
      card: {
        DEFAULT: tokens.colors.neutral.white,
        foreground: tokens.colors.neutral.gray[900],
        dark: tokens.colors.neutral.gray[900],
        'dark-foreground': tokens.colors.neutral.gray[100],
      },
      popover: {
        DEFAULT: tokens.colors.neutral.white,
        foreground: tokens.colors.neutral.gray[900],
        dark: tokens.colors.neutral.gray[900],
        'dark-foreground': tokens.colors.neutral.gray[100],
      },
      destructive: {
        DEFAULT: tokens.colors.semantic.error[500],
        foreground: tokens.colors.neutral.white,
      },
    },
    fontFamily: {
      sans: tokens.typography.fontFamily.sans,
      serif: tokens.typography.fontFamily.serif,
      mono: tokens.typography.fontFamily.mono,
    },
    fontSize: tokens.typography.fontSize,
    fontWeight: tokens.typography.fontWeight,
    lineHeight: tokens.typography.lineHeight,
    letterSpacing: tokens.typography.letterSpacing,
    borderRadius: tokens.borderRadius,
    boxShadow: tokens.shadows,
    zIndex: tokens.zIndex,
    extend: {
      spacing: tokens.spacing,
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
      },
      transitionTimingFunction: tokens.transitions.timing,
      transitionDuration: tokens.transitions.duration,
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'fade-out': 'fadeOut 0.5s ease-in-out',
        'slide-in-right': 'slideInRight 0.3s ease-in-out',
        'slide-out-right': 'slideOutRight 0.3s ease-in-out',
        'slide-in-bottom': 'slideInBottom 0.3s ease-in-out',
        'slide-out-bottom': 'slideOutBottom 0.3s ease-in-out',
        'spin-slow': 'spin 3s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOutRight: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        slideInBottom: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideOutBottom: {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(100%)' },
        },
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
export default config