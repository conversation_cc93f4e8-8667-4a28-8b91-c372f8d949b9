'use client';

import React, { useState } from 'react';
import Layout from '../components/Layout.js';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Package, 
  CreditCard, 
  Settings, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar,
  Download,
  Eye,
  Star,
  Clock,
  CheckCircle,
  Truck,
  FileText,
  Edit,
  Save,
  X
} from 'lucide-react';

// Mock user data
const userData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  company: 'Doe Enterprises',
  joinDate: 'March 2023',
  totalOrders: 24,
  totalSpent: 2450.00,
  loyaltyPoints: 1250,
  avatar: '/placeholder-avatar.jpg'
};

// Mock orders data
const ordersData = [
  {
    id: 'ORD-001',
    date: '2024-01-15',
    status: 'delivered',
    total: 125.50,
    items: ['Business Cards (500)', 'Letterhead (100)'],
    trackingNumber: 'TRK123456789'
  },
  {
    id: 'ORD-002',
    date: '2024-01-10',
    status: 'processing',
    total: 89.99,
    items: ['Custom Mugs (12)', 'Stickers (50)'],
    trackingNumber: 'TRK987654321'
  },
  {
    id: 'ORD-003',
    date: '2024-01-05',
    status: 'shipped',
    total: 245.00,
    items: ['Banner (3x6 ft)', 'Flyers (1000)'],
    trackingNumber: 'TRK456789123'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'delivered': return 'bg-success text-success-foreground';
    case 'shipped': return 'bg-primary text-primary-foreground';
    case 'processing': return 'bg-warning text-warning-foreground';
    case 'cancelled': return 'bg-destructive text-destructive-foreground';
    default: return 'bg-muted text-muted-foreground';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'delivered': return <CheckCircle className="h-4 w-4" />;
    case 'shipped': return <Truck className="h-4 w-4" />;
    case 'processing': return <Clock className="h-4 w-4" />;
    default: return <Package className="h-4 w-4" />;
  }
};

export default function AccountPage() {
  const [isEditing, setIsEditing] = useState(false);
  const [editedData, setEditedData] = useState(userData);

  const handleSave = () => {
    // Here you would typically save to your backend
    setIsEditing(false);
    console.log('Saving user data:', editedData);
  };

  const handleCancel = () => {
    setEditedData(userData);
    setIsEditing(false);
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-background via-secondary/5 to-primary/5">
        <div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
          {/* Header */}
          <div className="mb-8 animate-fade-in-up">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
                  My Account
                </h1>
                <p className="text-muted-foreground">
                  Manage your profile, orders, and preferences
                </p>
              </div>
              
              {/* Quick Stats */}
              <div className="flex gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{userData.totalOrders}</div>
                  <div className="text-xs text-muted-foreground">Orders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">${userData.totalSpent}</div>
                  <div className="text-xs text-muted-foreground">Spent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{userData.loyaltyPoints}</div>
                  <div className="text-xs text-muted-foreground">Points</div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 lg:w-auto lg:grid-cols-4 bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl p-1">
              <TabsTrigger value="profile" className="flex items-center gap-2 rounded-lg">
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">Profile</span>
              </TabsTrigger>
              <TabsTrigger value="orders" className="flex items-center gap-2 rounded-lg">
                <Package className="h-4 w-4" />
                <span className="hidden sm:inline">Orders</span>
              </TabsTrigger>
              <TabsTrigger value="addresses" className="flex items-center gap-2 rounded-lg">
                <MapPin className="h-4 w-4" />
                <span className="hidden sm:inline">Addresses</span>
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2 rounded-lg">
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Settings</span>
              </TabsTrigger>
            </TabsList>

            {/* Profile Tab */}
            <TabsContent value="profile" className="space-y-6 animate-fade-in-up">
              <Card className="shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-primary-hover flex items-center justify-center text-white font-semibold text-lg">
                      {userData.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    Profile Information
                  </CardTitle>
                  {!isEditing ? (
                    <Button onClick={() => setIsEditing(true)} variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  ) : (
                    <div className="flex gap-2">
                      <Button onClick={handleSave} size="sm" className="gradient-primary text-white">
                        <Save className="h-4 w-4 mr-2" />
                        Save
                      </Button>
                      <Button onClick={handleCancel} variant="outline" size="sm">
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                    </div>
                  )}
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      {isEditing ? (
                        <Input
                          id="name"
                          value={editedData.name}
                          onChange={(e) => setEditedData({...editedData, name: e.target.value})}
                        />
                      ) : (
                        <p className="text-foreground font-medium">{userData.name}</p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="company">Company</Label>
                      {isEditing ? (
                        <Input
                          id="company"
                          value={editedData.company}
                          onChange={(e) => setEditedData({...editedData, company: e.target.value})}
                        />
                      ) : (
                        <p className="text-foreground font-medium">{userData.company}</p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        {isEditing ? (
                          <Input
                            id="email"
                            type="email"
                            value={editedData.email}
                            onChange={(e) => setEditedData({...editedData, email: e.target.value})}
                          />
                        ) : (
                          <p className="text-foreground font-medium">{userData.email}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        {isEditing ? (
                          <Input
                            id="phone"
                            value={editedData.phone}
                            onChange={(e) => setEditedData({...editedData, phone: e.target.value})}
                          />
                        ) : (
                          <p className="text-foreground font-medium">{userData.phone}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 pt-4 border-t border-border/50">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      Member since {userData.joinDate}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <FileText className="h-6 w-6 text-primary" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-primary/5">
                      <Package className="h-6 w-6 text-primary" />
                      <span className="font-medium">Reorder</span>
                      <span className="text-xs text-muted-foreground">Previous orders</span>
                    </Button>
                    <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-primary/5">
                      <Download className="h-6 w-6 text-primary" />
                      <span className="font-medium">Invoices</span>
                      <span className="text-xs text-muted-foreground">Download receipts</span>
                    </Button>
                    <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-primary/5">
                      <Truck className="h-6 w-6 text-primary" />
                      <span className="font-medium">Track Order</span>
                      <span className="text-xs text-muted-foreground">Check status</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Loyalty Program */}
              <Card className="shadow-modern-lg border-0 bg-gradient-to-r from-primary/10 to-primary/5 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Star className="h-6 w-6 text-primary" />
                    Loyalty Program
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-2xl font-bold text-primary">{userData.loyaltyPoints} Points</p>
                      <p className="text-sm text-muted-foreground">Available to redeem</p>
                    </div>
                    <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
                      Redeem Points
                    </Button>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{width: '65%'}}></div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Earn 750 more points to reach Gold status
                  </p>

                  {/* Points earning info */}
                  <div className="mt-4 p-3 bg-background/50 rounded-lg">
                    <h4 className="font-medium text-foreground mb-2">How to earn points:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• $1 spent = 1 point</li>
                      <li>• Product reviews = 50 points</li>
                      <li>• Referrals = 200 points</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Orders Tab */}
            <TabsContent value="orders" className="space-y-6 animate-fade-in-up">
              <Card className="shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Package className="h-6 w-6 text-primary" />
                    Order History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {ordersData.map((order) => (
                      <div key={order.id} className="border border-border/50 rounded-xl p-4 hover:shadow-modern transition-all duration-300">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-3">
                              <h3 className="font-semibold text-foreground">Order {order.id}</h3>
                              <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                                {getStatusIcon(order.status)}
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {new Date(order.date).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </p>
                            <div className="text-sm text-muted-foreground">
                              {order.items.join(', ')}
                            </div>
                            {order.trackingNumber && (
                              <p className="text-xs text-muted-foreground">
                                Tracking: {order.trackingNumber}
                              </p>
                            )}
                          </div>

                          <div className="flex flex-col md:items-end gap-2">
                            <p className="text-lg font-bold text-primary">${order.total}</p>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-2" />
                                View
                              </Button>
                              <Button variant="outline" size="sm">
                                <Download className="h-4 w-4 mr-2" />
                                Invoice
                              </Button>
                              {order.status === 'delivered' && (
                                <Button variant="outline" size="sm">
                                  Reorder
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 text-center">
                    <Button variant="outline" className="border-2">
                      Load More Orders
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Addresses Tab */}
            <TabsContent value="addresses" className="space-y-6 animate-fade-in-up">
              <Card className="shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="flex items-center gap-3">
                    <MapPin className="h-6 w-6 text-primary" />
                    Saved Addresses
                  </CardTitle>
                  <Button className="gradient-primary text-white">
                    Add New Address
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Default Address */}
                    <div className="border border-border/50 rounded-xl p-4 relative">
                      <Badge className="absolute top-2 right-2 bg-primary text-primary-foreground">
                        Default
                      </Badge>
                      <div className="space-y-2 pt-6">
                        <h3 className="font-semibold text-foreground">Home Address</h3>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p>123 Main Street</p>
                          <p>Apartment 4B</p>
                          <p>New York, NY 10001</p>
                          <p>United States</p>
                        </div>
                        <div className="flex gap-2 pt-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Work Address */}
                    <div className="border border-border/50 rounded-xl p-4">
                      <div className="space-y-2">
                        <h3 className="font-semibold text-foreground">Work Address</h3>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p>456 Business Ave</p>
                          <p>Suite 200</p>
                          <p>New York, NY 10002</p>
                          <p>United States</p>
                        </div>
                        <div className="flex gap-2 pt-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Set as Default
                          </Button>
                          <Button variant="outline" size="sm">
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6 animate-fade-in-up">
              <Card className="shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Settings className="h-6 w-6 text-primary" />
                    Account Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Notifications */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Notifications</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground">Order Updates</p>
                          <p className="text-sm text-muted-foreground">Get notified about order status changes</p>
                        </div>
                        <input type="checkbox" defaultChecked className="toggle" />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground">Promotional Emails</p>
                          <p className="text-sm text-muted-foreground">Receive special offers and promotions</p>
                        </div>
                        <input type="checkbox" defaultChecked className="toggle" />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground">SMS Notifications</p>
                          <p className="text-sm text-muted-foreground">Get text messages for urgent updates</p>
                        </div>
                        <input type="checkbox" className="toggle" />
                      </div>
                    </div>
                  </div>

                  {/* Security */}
                  <div className="space-y-4 pt-6 border-t border-border/50">
                    <h3 className="text-lg font-semibold text-foreground">Security</h3>
                    <div className="space-y-3">
                      <Button variant="outline" className="w-full justify-start">
                        <CreditCard className="h-4 w-4 mr-2" />
                        Change Password
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <FileText className="h-4 w-4 mr-2" />
                        Download My Data
                      </Button>
                      <Button variant="destructive" className="w-full justify-start">
                        <X className="h-4 w-4 mr-2" />
                        Delete Account
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
}
