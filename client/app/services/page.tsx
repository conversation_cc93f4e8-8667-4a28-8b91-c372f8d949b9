'use client';

import Layout from '../components/Layout.js';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageCircle } from 'lucide-react';

// Dummy data for services
const services = [
  {
    id: 's1',
    name: 'Custom T-Shirt Printing',
    description: 'Personalized t-shirts for events, teams, or promotional giveaways. Various styles available, including screen printing and direct-to-garment.',
    startingPrice: 'From $18.00',
    imageUrl: '/placeholder-tshirts.jpg', // Re-using product image for now
  },
  {
    id: 's2',
    name: 'Graphic Design Services',
    description: 'Professional design services to create stunning visuals for your brand, including logos, brochures, and marketing materials.',
    startingPrice: 'Quote Required',
    imageUrl: '/placeholder-logo.jpg', // Re-using product image for now
  },
  {
    id: 's3',
    name: 'Large Format Printing',
    description: 'Durable, vibrant banners, posters, and signs that make a big impact, perfect for events and outdoor advertising.',
    startingPrice: 'From $75.00',
    imageUrl: '/placeholder-banners.jpg', // Re-using product image for now
  },
  {
    id: 's4',
    name: 'Brochure & Flyer Design',
    description: 'Engaging brochures and flyers designed to capture attention and convey your message effectively. Available in various folds and sizes.',
    startingPrice: 'From $50.00',
    imageUrl: '/placeholder-brochures.jpg', // Re-using product image for now
  },
];

export default function ServicesPage() {
  const handleRequestQuote = (serviceName) => {
    const message = encodeURIComponent(`Hello, I would like to request a quote for your ${serviceName} service.`);
    // Replace with your WhatsApp number
    const whatsappUrl = `https://wa.me/1234567890?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <Layout>
      <main className="container mx-auto px-6 py-16 md:py-24">
        <h1 className="text-4xl md:text-5xl font-extrabold text-center mb-12 text-foreground">
          Our Services
        </h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {services.map((service) => (
            <Card key={service.id} className="flex flex-col overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-300 ease-in-out transform hover:-translate-y-1">
              <div className="relative w-full h-48 bg-muted overflow-hidden">
                <Image
                  src={service.imageUrl}
                  alt={service.name}
                  layout="fill"
                  objectFit="cover"
                  className="transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-xl font-semibold text-foreground leading-tight">
                  {service.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0 flex-grow">
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                  {service.description}
                </p>
                <p className="text-lg font-bold text-primary">
                  {service.startingPrice}
                </p>
              </CardContent>
              <CardFooter className="p-4 pt-0">
                <Button onClick={() => handleRequestQuote(service.name)} className="w-full">
                  <MessageCircle className="h-4 w-4 mr-2" /> Request Quote
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </main>
    </Layout>
  );
}
