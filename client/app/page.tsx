import Layout from './components/Layout.js';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Printer, Paintbrush, Users } from 'lucide-react'; // Importing icons
import HeroSlider from './components/HeroSlider.js';
import ProductQuickViewModal from './components/ProductQuickViewModal.js';

// Dummy data for best-selling products
const bestSellerProducts = [
  {
    id: '1',
    name: 'Premium Business Cards',
    description: 'High-quality, custom-designed business cards to make a lasting impression.',
    price: 'Starting at $25.00',
    imageUrl: '/placeholder-business-cards.jpg',
    details: 'Available in various sizes and paper types. Fast turnaround times and competitive pricing. Design services also available.'
  },
  {
    id: '3',
    name: 'Custom Mugs',
    description: 'Personalized mugs for gifts, promotions, or corporate branding. Dishwasher and microwave safe.',
    price: 'Starting at $10.00',
    imageUrl: '/placeholder-mugs.jpg',
    details: 'Choose from a wide range of colors and sizes. Perfect for corporate events, sports teams, and personal gifts. Minimum order quantities may apply.'
  },
  {
    id: '6',
    name: 'Posters',
    description: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.',
    price: 'Starting at $15.00',
    imageUrl: '/placeholder-posters.jpg',
    details: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.'
  },
];

export default function Home() {
  return (
    <Layout>
      <HeroSlider />

      {/* Best Seller Section */}
      <section className="container mx-auto px-6 py-16 md:py-24">
        <div className="flex justify-between items-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground">
            Best Sellers
          </h2>
          <Button asChild variant="outline">
            <Link href="/products">View All Products</Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {bestSellerProducts.map((item) => (
            <Card key={item.id} className="flex flex-col overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-300 ease-in-out transform hover:-translate-y-1">
              <div className="relative w-full h-48 bg-muted overflow-hidden">
                <Image
                  src={item.imageUrl}
                  alt={item.name}
                  layout="fill"
                  objectFit="cover"
                  className="transition-transform duration-300 hover:scale-105"
                />
              </div>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-xl font-semibold text-foreground leading-tight">
                  {item.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0 flex-grow">
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                  {item.description}
                </p>
                <p className="text-lg font-bold text-primary">
                  {item.price}
                </p>
                {/* Star Ratings Placeholder */}
                <div className="text-muted-foreground text-sm">
                  <span>☆☆☆☆☆</span>
                  <span className="ml-1 text-xs">(0 Reviews)</span>
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-0">
                <ProductQuickViewModal product={item}>
                  <Button className="w-full">Buy now</Button>
                </ProductQuickViewModal>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>

      {/* Category Banners Section */}
      <section className="container mx-auto px-6 py-16 md:py-24">
        <h2 className="text-3xl md:text-4xl font-bold text-center text-foreground mb-12">
          Explore
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Category Banner 1 */}
          <Card className="flex flex-col overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-300 ease-in-out group">
            <div className="relative w-full h-48 bg-muted overflow-hidden">
              <Image
                src="/placeholder-design.svg"
                alt="Design Services"
                layout="fill"
                objectFit="cover"
                className="transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-xl font-semibold text-foreground leading-tight">Design Services</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0 flex-grow">
              <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                Professional graphic design for logos, branding, and marketing materials.
              </p>
            </CardContent>
            <CardFooter className="p-4 pt-0">
              <Button asChild className="w-full">
                <Link href="/services">View Services</Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Category Banner 2 */}
          <Card className="flex flex-col overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-300 ease-in-out group">
            <div className="relative w-full h-48 bg-muted overflow-hidden">
              <Image
                src="/placeholder-large-format.svg"
                alt="Large Format Printing"
                layout="fill"
                objectFit="cover"
                className="transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-xl font-semibold text-foreground leading-tight">Large Format Printing</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0 flex-grow">
              <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                Banners, posters, and signs for events, promotions, and outdoor advertising.
              </p>
            </CardContent>
            <CardFooter className="p-4 pt-0">
              <Button asChild className="w-full">
                <Link href="/services">View Services</Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Category Banner 3 */}
          <Card className="flex flex-col overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-300 ease-in-out group">
            <div className="relative w-full h-48 bg-muted overflow-hidden">
              <Image
                src="/placeholder-apparel.svg"
                alt="Custom Apparel"
                layout="fill"
                objectFit="cover"
                className="transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-xl font-semibold text-foreground leading-tight">Custom Apparel</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0 flex-grow">
              <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                Personalized t-shirts, hoodies, and uniforms for teams, events, or businesses.
              </p>
            </CardContent>
            <CardFooter className="p-4 pt-0">
              <Button asChild className="w-full">
                <Link href="/services">View Services</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="bg-muted py-16 md:py-24">
        <div className="container mx-auto px-6 text-center max-w-3xl">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-8">
            Stay Updated with Multiprints
          </h2>
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-grow p-3 border border-border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 text-foreground"
            />
            <Button className="bg-primary text-primary-foreground rounded-md px-8 py-3 text-lg hover:bg-primary/90 transition-colors duration-200">
              Subscribe
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Join our mailing list for the latest news, promotions, and printing tips.
          </p>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-16 md:py-24 bg-background">
        <div className="container mx-auto px-6 text-center max-w-2xl">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-12">
            What Our Clients Say
          </h2>
          <Card className="p-8 shadow-sm border border-border">
            <CardContent className="flex flex-col items-center">
              <div className="relative w-24 h-24 rounded-full overflow-hidden border-2 border-border mb-6">
                <img src="/placeholder-avatar.jpg" alt="Customer Photo" className="object-cover w-full h-full" />
              </div>
              <p className="italic text-[1.1rem] text-muted-foreground mb-6 leading-relaxed">
                "Multiprints consistently delivers high-quality prints with exceptional speed and professionalism. They are our go-to for all our business printing needs!"
              </p>
              <p className="text-sm font-semibold uppercase tracking-wider text-foreground">
                John Doe
              </p>
              <p className="text-xs uppercase tracking-wider text-muted-foreground">
                Small Business Owner
              </p>
            </CardContent>
          </Card>
        </div>
      </section>
    </Layout>
  );
}