import Layout from './components/Layout.js';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Printer, Paintbrush, Users } from 'lucide-react'; // Importing icons
import HeroSlider from './components/HeroSlider.js';
import ProductQuickViewModal from './components/ProductQuickViewModal.js';

// Dummy data for best-selling products
const bestSellerProducts = [
  {
    id: '1',
    name: 'Premium Business Cards',
    description: 'High-quality, custom-designed business cards to make a lasting impression.',
    price: 'Starting at $25.00',
    imageUrl: '/placeholder-business-cards.jpg',
    details: 'Available in various sizes and paper types. Fast turnaround times and competitive pricing. Design services also available.'
  },
  {
    id: '3',
    name: 'Custom Mugs',
    description: 'Personalized mugs for gifts, promotions, or corporate branding. Dishwasher and microwave safe.',
    price: 'Starting at $10.00',
    imageUrl: '/placeholder-mugs.jpg',
    details: 'Choose from a wide range of colors and sizes. Perfect for corporate events, sports teams, and personal gifts. Minimum order quantities may apply.'
  },
  {
    id: '6',
    name: 'Posters',
    description: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.',
    price: 'Starting at $15.00',
    imageUrl: '/placeholder-posters.jpg',
    details: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.'
  },
];

export default function Home() {
  return (
    <Layout>
      <HeroSlider />

      {/* Best Seller Section */}
      <section className="container mx-auto px-6 py-20 md:py-32">
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary font-medium text-sm mb-6">
            <span>⭐</span>
            Customer Favorites
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4 text-balance">
            Our Best Sellers
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            Discover our most popular printing solutions trusted by thousands of satisfied customers
          </p>
          <Button asChild variant="outline" size="lg" className="border-2 hover:bg-primary/5">
            <Link href="/products">View All Products</Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {bestSellerProducts.map((item, index) => (
            <Card key={item.id} className={`group flex flex-col overflow-hidden rounded-2xl border-0 shadow-modern hover:shadow-modern-xl transition-all duration-500 ease-out hover-lift bg-card/50 backdrop-blur-sm animate-fade-in-up animation-delay-${(index + 1) * 100}`}>
              {/* Image Container */}
              <div className="relative w-full h-64 bg-gradient-to-br from-primary/5 to-secondary/5 overflow-hidden">
                <Image
                  src={item.imageUrl}
                  alt={item.name}
                  layout="fill"
                  objectFit="cover"
                  className="transition-all duration-500 group-hover:scale-110"
                />
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Badge */}
                <div className="absolute top-4 left-4 px-3 py-1 bg-primary text-primary-foreground text-xs font-semibold rounded-full">
                  Best Seller
                </div>

                {/* Quick Action */}
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <button className="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-foreground hover:bg-white transition-colors duration-200 shadow-lg">
                    <span className="text-lg">♡</span>
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex flex-col flex-grow p-6">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-xl font-semibold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
                    {item.name}
                  </CardTitle>
                </CardHeader>

                <CardContent className="p-0 flex-grow space-y-4">
                  <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
                    {item.description}
                  </p>

                  {/* Rating */}
                  <div className="flex items-center gap-2">
                    <div className="flex text-yellow-400">
                      {'★★★★★'.split('').map((star, i) => (
                        <span key={i} className="text-sm">{star}</span>
                      ))}
                    </div>
                    <span className="text-xs text-muted-foreground">(4.9)</span>
                  </div>

                  {/* Price */}
                  <div className="flex items-baseline gap-2">
                    <p className="text-2xl font-bold text-primary">
                      {item.price}
                    </p>
                    <span className="text-sm text-muted-foreground line-through">
                      ${(parseFloat(item.price.replace(/[^0-9.]/g, '')) * 1.3).toFixed(2)}
                    </span>
                  </div>
                </CardContent>

                <CardFooter className="p-0 pt-6">
                  <div className="w-full space-y-3">
                    <ProductQuickViewModal product={item}>
                      <Button className="w-full gradient-primary text-white hover:opacity-90 transition-all duration-300 shadow-modern group">
                        <span>Get Quote</span>
                        <span className="ml-2 transition-transform group-hover:translate-x-1">→</span>
                      </Button>
                    </ProductQuickViewModal>
                    <Button variant="outline" className="w-full border-2 hover:bg-primary/5 transition-all duration-300">
                      View Details
                    </Button>
                  </div>
                </CardFooter>
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* Category Banners Section */}
      <section className="bg-gradient-to-br from-secondary/20 via-background to-primary/5 py-20 md:py-32">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16 animate-fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4 text-balance">
              Explore Our Services
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              From design to delivery, we offer comprehensive printing solutions for all your needs
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-10">
            {/* Category Banner 1 */}
            <Card className="group flex flex-col overflow-hidden rounded-2xl border-0 shadow-modern hover:shadow-modern-xl transition-all duration-500 ease-out hover-lift bg-card/50 backdrop-blur-sm animate-fade-in-up animation-delay-100">
              <div className="relative w-full h-56 bg-gradient-to-br from-primary/10 to-primary/5 overflow-hidden">
                <Image
                  src="/placeholder-design.svg"
                  alt="Design Services"
                  layout="fill"
                  objectFit="cover"
                  className="transition-all duration-500 group-hover:scale-110 opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/20 via-transparent to-transparent"></div>

                {/* Icon */}
                <div className="absolute top-6 left-6 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                  <Paintbrush className="h-6 w-6 text-primary" />
                </div>
              </div>

              <div className="p-6 flex-grow flex flex-col">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-2xl font-semibold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
                    Design Services
                  </CardTitle>
                </CardHeader>

                <CardContent className="p-0 flex-grow">
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    Professional graphic design for logos, branding, and marketing materials that make your business stand out.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Logo Design</span>
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Branding</span>
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Marketing</span>
                  </div>
                </CardContent>

                <CardFooter className="p-0 pt-6">
                  <Button asChild className="w-full gradient-primary text-white hover:opacity-90 transition-all duration-300 shadow-modern group">
                    <Link href="/services" className="flex items-center justify-center gap-2">
                      View Services
                      <span className="transition-transform group-hover:translate-x-1">→</span>
                    </Link>
                  </Button>
                </CardFooter>
              </div>
            </Card>

            {/* Category Banner 2 */}
            <Card className="group flex flex-col overflow-hidden rounded-2xl border-0 shadow-modern hover:shadow-modern-xl transition-all duration-500 ease-out hover-lift bg-card/50 backdrop-blur-sm animate-fade-in-up animation-delay-200">
              <div className="relative w-full h-56 bg-gradient-to-br from-secondary/10 to-secondary/5 overflow-hidden">
                <Image
                  src="/placeholder-large-format.svg"
                  alt="Large Format Printing"
                  layout="fill"
                  objectFit="cover"
                  className="transition-all duration-500 group-hover:scale-110 opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-secondary/20 via-transparent to-transparent"></div>

                {/* Icon */}
                <div className="absolute top-6 left-6 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                  <Printer className="h-6 w-6 text-primary" />
                </div>
              </div>

              <div className="p-6 flex-grow flex flex-col">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-2xl font-semibold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
                    Large Format Printing
                  </CardTitle>
                </CardHeader>

                <CardContent className="p-0 flex-grow">
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    Banners, posters, and signs for events, promotions, and outdoor advertising that capture attention.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Banners</span>
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Posters</span>
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Signage</span>
                  </div>
                </CardContent>

                <CardFooter className="p-0 pt-6">
                  <Button asChild className="w-full gradient-primary text-white hover:opacity-90 transition-all duration-300 shadow-modern group">
                    <Link href="/services" className="flex items-center justify-center gap-2">
                      View Services
                      <span className="transition-transform group-hover:translate-x-1">→</span>
                    </Link>
                  </Button>
                </CardFooter>
              </div>
            </Card>

            {/* Category Banner 3 */}
            <Card className="group flex flex-col overflow-hidden rounded-2xl border-0 shadow-modern hover:shadow-modern-xl transition-all duration-500 ease-out hover-lift bg-card/50 backdrop-blur-sm animate-fade-in-up animation-delay-300">
              <div className="relative w-full h-56 bg-gradient-to-br from-accent/10 to-accent/5 overflow-hidden">
                <Image
                  src="/placeholder-apparel.svg"
                  alt="Custom Apparel"
                  layout="fill"
                  objectFit="cover"
                  className="transition-all duration-500 group-hover:scale-110 opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-accent/20 via-transparent to-transparent"></div>

                {/* Icon */}
                <div className="absolute top-6 left-6 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
                  <Users className="h-6 w-6 text-primary" />
                </div>
              </div>

              <div className="p-6 flex-grow flex flex-col">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-2xl font-semibold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
                    Custom Apparel
                  </CardTitle>
                </CardHeader>

                <CardContent className="p-0 flex-grow">
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    Personalized t-shirts, hoodies, and uniforms for teams, events, or businesses that build unity.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">T-Shirts</span>
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Hoodies</span>
                    <span className="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full">Uniforms</span>
                  </div>
                </CardContent>

                <CardFooter className="p-0 pt-6">
                  <Button asChild className="w-full gradient-primary text-white hover:opacity-90 transition-all duration-300 shadow-modern group">
                    <Link href="/services" className="flex items-center justify-center gap-2">
                      View Services
                      <span className="transition-transform group-hover:translate-x-1">→</span>
                    </Link>
                  </Button>
                </CardFooter>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 gradient-hero opacity-10"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-background/80 via-background/60 to-background/80"></div>

        <div className="relative z-10 container mx-auto px-6 text-center max-w-4xl">
          <div className="animate-fade-in-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary font-medium text-sm mb-6">
              <span>📧</span>
              Stay Connected
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6 text-balance">
              Never Miss Our Latest Updates
            </h2>

            <p className="text-lg text-muted-foreground mb-12 max-w-2xl mx-auto">
              Get exclusive access to new products, special offers, and printing tips delivered straight to your inbox.
            </p>

            <div className="max-w-md mx-auto">
              <div className="flex flex-col sm:flex-row gap-3 p-2 bg-background/80 backdrop-blur-sm rounded-2xl border border-border/50 shadow-modern-lg">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-grow px-4 py-3 bg-transparent border-0 focus:outline-none focus:ring-0 text-foreground placeholder:text-muted-foreground"
                />
                <Button className="gradient-primary text-white hover:opacity-90 transition-all duration-300 shadow-modern px-8 py-3 rounded-xl group">
                  <span>Subscribe</span>
                  <span className="ml-2 transition-transform group-hover:translate-x-1">→</span>
                </Button>
              </div>

              <p className="text-sm text-muted-foreground mt-4 flex items-center justify-center gap-2">
                <span>🔒</span>
                We respect your privacy. Unsubscribe at any time.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-20 md:py-32 bg-gradient-to-br from-secondary/10 via-background to-primary/5">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16 animate-fade-in-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary font-medium text-sm mb-6">
              <span>💬</span>
              Client Stories
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4 text-balance">
              Loved by Thousands of Customers
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              See what our satisfied customers have to say about our printing services
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Testimonial 1 */}
            <Card className="p-8 shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm rounded-2xl hover-lift animate-fade-in-up animation-delay-100">
              <CardContent className="flex flex-col items-center text-center space-y-6">
                <div className="flex text-yellow-400 text-lg">
                  {'★★★★★'.split('').map((star, i) => (
                    <span key={i}>{star}</span>
                  ))}
                </div>

                <p className="text-muted-foreground leading-relaxed italic">
                  "Multiprints consistently delivers high-quality prints with exceptional speed and professionalism. They are our go-to for all our business printing needs!"
                </p>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-primary-hover flex items-center justify-center text-white font-semibold">
                    JD
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-foreground">John Doe</p>
                    <p className="text-sm text-muted-foreground">Small Business Owner</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 2 */}
            <Card className="p-8 shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm rounded-2xl hover-lift animate-fade-in-up animation-delay-200">
              <CardContent className="flex flex-col items-center text-center space-y-6">
                <div className="flex text-yellow-400 text-lg">
                  {'★★★★★'.split('').map((star, i) => (
                    <span key={i}>{star}</span>
                  ))}
                </div>

                <p className="text-muted-foreground leading-relaxed italic">
                  "Amazing design team and fast turnaround! Our marketing materials look incredible and have really helped boost our brand presence."
                </p>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-secondary to-secondary-hover flex items-center justify-center text-white font-semibold">
                    SM
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-foreground">Sarah Miller</p>
                    <p className="text-sm text-muted-foreground">Marketing Director</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 3 */}
            <Card className="p-8 shadow-modern-lg border-0 bg-card/50 backdrop-blur-sm rounded-2xl hover-lift animate-fade-in-up animation-delay-300">
              <CardContent className="flex flex-col items-center text-center space-y-6">
                <div className="flex text-yellow-400 text-lg">
                  {'★★★★★'.split('').map((star, i) => (
                    <span key={i}>{star}</span>
                  ))}
                </div>

                <p className="text-muted-foreground leading-relaxed italic">
                  "Professional service from start to finish. The quality of our custom apparel exceeded expectations and the team was incredibly helpful throughout."
                </p>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-accent to-accent-foreground flex items-center justify-center text-white font-semibold">
                    MJ
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-foreground">Mike Johnson</p>
                    <p className="text-sm text-muted-foreground">Event Coordinator</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </Layout>
  );
}