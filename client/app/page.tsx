import { PublicLayout } from "@/components/layouts/public-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, ChevronRight, Printer, Star } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Sample featured products
const featuredProducts = [
  {
    id: 1,
    name: "Premium Business Cards",
    description: "Make a lasting impression with our high-quality business cards.",
    price: 25.00,
    image: "/placeholder.svg",
    rating: 4.9,
    reviewCount: 124,
    category: "Business Essentials"
  },
  {
    id: 2,
    name: "Custom Brochures",
    description: "Showcase your products and services with professional brochures.",
    price: 45.00,
    image: "/placeholder.svg",
    rating: 4.8,
    reviewCount: 89,
    category: "Marketing Materials"
  },
  {
    id: 3,
    name: "Large Format Posters",
    description: "Grab attention with vibrant, high-resolution posters.",
    price: 35.00,
    image: "/placeholder.svg",
    rating: 4.7,
    reviewCount: 56,
    category: "Advertising"
  },
  {
    id: 4,
    name: "Custom T-Shirts",
    description: "Quality custom t-shirts for teams, events, or promotions.",
    price: 18.00,
    image: "/placeholder.svg",
    rating: 4.9,
    reviewCount: 112,
    category: "Apparel"
  }
]

// Sample services
const services = [
  {
    title: "Design Services",
    description: "Professional graphic design for logos, branding, and marketing materials.",
    icon: <Printer className="h-10 w-10 text-primary" />,
    link: "/services/design"
  },
  {
    title: "Digital Printing",
    description: "High-quality digital printing for small to medium runs with quick turnaround.",
    icon: <Printer className="h-10 w-10 text-primary" />,
    link: "/services/digital-printing"
  },
  {
    title: "Large Format",
    description: "Banners, posters, and signs for events, promotions, and outdoor advertising.",
    icon: <Printer className="h-10 w-10 text-primary" />,
    link: "/services/large-format"
  },
  {
    title: "Promotional Items",
    description: "Custom branded merchandise and promotional products for your business.",
    icon: <Printer className="h-10 w-10 text-primary" />,
    link: "/services/promotional"
  }
]

// Sample testimonials
const testimonials = [
  {
    quote: "Multiprints delivered exceptional quality business cards that exceeded our expectations. Their attention to detail and customer service is outstanding.",
    author: "Sarah Johnson",
    company: "Johnson & Associates",
    image: "/placeholder.svg"
  },
  {
    quote: "We've been using Multiprints for all our marketing materials for over 3 years. Their quality and consistency keeps us coming back.",
    author: "Michael Chen",
    company: "Innovate Tech",
    image: "/placeholder.svg"
  },
  {
    quote: "The team at Multiprints helped us design and print amazing promotional materials for our product launch. Highly recommended!",
    author: "Emily Rodriguez",
    company: "Brand Forward",
    image: "/placeholder.svg"
  }
]

export default function Home() {
  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 via-background to-secondary-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
        <div className="container py-16 md:py-24 lg:py-32">
          <div className="grid gap-8 md:grid-cols-2 md:gap-12 items-center">
            <div className="space-y-6 animate-fade-in-up">
              <Badge variant="secondary" className="mb-4">
                Professional Printing Services
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
                <span className="text-gradient-primary">Quality Printing</span> for Your Business
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground">
                From business cards to banners, we provide high-quality printing solutions to help your business stand out.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="shadow-button">
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button size="lg" variant="outline">
                  View Products
                </Button>
              </div>
              <div className="flex items-center gap-4 pt-4">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="h-8 w-8 rounded-full bg-primary-100 border-2 border-background flex items-center justify-center text-primary-700 text-xs font-medium">
                      {String.fromCharCode(64 + i)}
                    </div>
                  ))}
                </div>
                <div className="text-sm">
                  <span className="font-semibold">500+</span> satisfied customers
                </div>
              </div>
            </div>
            <div className="relative animate-fade-in-up animation-delay-300">
              <div className="relative h-[400px] md:h-[500px] rounded-2xl overflow-hidden shadow-xl">
                <Image 
                  src="/placeholder.svg" 
                  alt="Printing services" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="absolute -bottom-6 -left-6 bg-card p-4 rounded-xl shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="flex text-yellow-400">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <Star key={i} className="h-4 w-4 fill-current" />
                    ))}
                  </div>
                  <span className="font-medium">4.9/5</span>
                </div>
                <p className="text-sm text-muted-foreground">Based on 200+ reviews</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Trusted by section */}
        <div className="container pb-16 md:pb-24">
          <div className="border-t pt-12">
            <p className="text-center text-sm text-muted-foreground mb-8">
              TRUSTED BY LEADING COMPANIES
            </p>
            <div className="flex flex-wrap justify-center gap-8 md:gap-12 opacity-70">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-8 flex items-center">
                  <div className="w-24 h-8 bg-muted rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Featured Products Section */}
      <section className="py-16 md:py-24">
        <div className="container">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              Featured Products
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Our Most Popular Printing Solutions
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover our best-selling products that help businesses make a lasting impression.
            </p>
          </div>
          
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {featuredProducts.map((product) => (
              <Card key={product.id} className="overflow-hidden hover-lift">
                <div className="relative h-48">
                  <Image 
                    src={product.image} 
                    alt={product.name} 
                    fill 
                    className="object-cover"
                  />
                  <div className="absolute top-2 left-2">
                    <Badge className="bg-primary/90 backdrop-blur-sm">
                      {product.category}
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {product.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{product.rating}</span>
                      <span className="text-xs text-muted-foreground">
                        ({product.reviewCount})
                      </span>
                    </div>
                    <div className="font-semibold">
                      ${product.price.toFixed(2)}
                    </div>
                  </div>
                  <Button className="w-full mt-4">
                    View Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button variant="outline" size="lg" asChild>
              <Link href="/products">
                View All Products
                <ChevronRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
      
      {/* Services Section */}
      <section className="py-16 md:py-24 bg-muted/30">
        <div className="container">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              Our Services
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Comprehensive Printing Solutions
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              From design to delivery, we offer end-to-end printing services to meet all your needs.
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {services.map((service, index) => (
              <Card key={index} className="hover-lift">
                <CardContent className="p-6">
                  <div className="mb-4 p-3 rounded-lg bg-primary/10 w-fit">
                    {service.icon}
                  </div>
                  <h3 className="font-semibold text-xl mb-2">{service.title}</h3>
                  <p className="text-muted-foreground mb-4">
                    {service.description}
                  </p>
                  <Button variant="outline" asChild>
                    <Link href={service.link}>
                      Learn More
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      {/* Why Choose Us Section */}
      <section className="py-16 md:py-24">
        <div className="container">
          <div className="grid gap-12 md:grid-cols-2 items-center">
            <div className="space-y-6">
              <Badge variant="secondary">Why Choose Us</Badge>
              <h2 className="text-3xl md:text-4xl font-bold">
                Quality Printing Services You Can Trust
              </h2>
              <p className="text-muted-foreground">
                At Multiprints, we combine cutting-edge technology with expert craftsmanship to deliver exceptional printing results every time.
              </p>
              
              <div className="space-y-4 pt-4">
                {[
                  "Premium quality materials and finishes",
                  "Fast turnaround times and reliable delivery",
                  "Expert design services and consultation",
                  "Eco-friendly printing options",
                  "Competitive pricing with no hidden fees"
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <span>{item}</span>
                  </div>
                ))}
              </div>
              
              <Button size="lg" className="mt-4">
                Learn More About Us
              </Button>
            </div>
            
            <div className="relative">
              <div className="relative h-[400px] rounded-2xl overflow-hidden shadow-xl">
                <Image 
                  src="/placeholder.svg" 
                  alt="Printing facility" 
                  fill 
                  className="object-cover"
                />
              </div>
              <div className="absolute -bottom-6 -right-6 bg-card p-6 rounded-xl shadow-lg max-w-[200px]">
                <div className="text-4xl font-bold text-primary mb-2">15+</div>
                <p className="text-sm">Years of experience in the printing industry</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Testimonials Section */}
      <section className="py-16 md:py-24 bg-muted/30">
        <div className="container">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              Testimonials
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              What Our Customers Say
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our satisfied customers have to say.
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="hover-lift">
                <CardContent className="p-6">
                  <div className="flex justify-center mb-6">
                    <div className="flex text-yellow-400">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <Star key={i} className="h-5 w-5 fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-center italic mb-6">
                    &ldquo;{testimonial.quote}&rdquo;
                  </p>
                  <div className="flex items-center justify-center gap-4">
                    <div className="h-12 w-12 rounded-full overflow-hidden relative">
                      <Image 
                        src={testimonial.image} 
                        alt={testimonial.author} 
                        fill 
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-semibold">{testimonial.author}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.company}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 md:py-24">
        <div className="container">
          <div className="bg-gradient-to-br from-primary-600 to-primary-800 rounded-3xl p-8 md:p-12 lg:p-16 text-white">
            <div className="grid gap-8 md:grid-cols-2 items-center">
              <div className="space-y-6">
                <h2 className="text-3xl md:text-4xl font-bold">
                  Ready to Start Your Next Printing Project?
                </h2>
                <p className="text-primary-100">
                  Contact us today for a free consultation and quote. Our team is ready to help bring your vision to life.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button size="lg" variant="secondary">
                    Get a Quote
                  </Button>
                  <Button size="lg" variant="outline" className="bg-transparent text-white border-white hover:bg-white/10">
                    Contact Us
                  </Button>
                </div>
              </div>
              <div className="hidden md:block">
                <div className="relative h-[300px]">
                  <Image 
                    src="/placeholder.svg" 
                    alt="Printing project" 
                    fill 
                    className="object-cover rounded-xl"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PublicLayout>
  )
}