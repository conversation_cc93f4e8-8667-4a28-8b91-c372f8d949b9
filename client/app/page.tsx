"use client"

import { PublicLayout } from "@/components/layouts/public-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowRight, CheckCircle, Palette, Zap, Award } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import HeroSlider from "./components/HeroSlider.js"

// Re-imagined, cleaner services section
const coreServices = [
  {
    title: "Business & Marketing",
    description: "Business cards, brochures, flyers, and posters that make an impact.",
    icon: <Zap className="h-8 w-8 text-primary" />,
    link: "/products"
  },
  {
    title: "Apparel & Promo",
    description: "Custom t-shirts, mugs, and promotional items for your brand or event.",
    icon: <Award className="h-8 w-8 text-primary" />,
    link: "/products"
  },
  {
    title: "Design & Branding",
    description: "Professional design services to bring your vision to life, from logos to full brand kits.",
    icon: <Palette className="h-8 w-8 text-primary" />,
    link: "/services"
  }
]

// Simplified 'Why Choose Us' section
const whyChooseUs = [
  {
    title: "Unmatched Quality",
    description: "We use state-of-the-art equipment and premium materials to ensure every print is perfect."
  },
  {
    title: "Fast Turnaround",
    description: "Get your prints when you need them with our efficient processing and reliable delivery."
  },
  {
    title: "Expert Support",
    description: "Our team of design and print specialists are here to help you every step of the way."
  }
]

export default function ModernHomePage() {
  return (
    <PublicLayout>
      {/* Use the dynamic Hero Slider for an engaging start */}
      <HeroSlider />

      {/* Core Services Section */}
      <section className="py-16 md:py-24 bg-surface">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Everything You Need to Print
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              From essential marketing materials to custom promotional items, we provide a full spectrum of printing services.
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-3">
            {coreServices.map((service, index) => (
              <Card key={index} className="text-center p-8 hover-lift border-2 border-transparent hover:border-primary/10 hover:shadow-xl">
                <div className="flex justify-center mb-6">
                  <div className="p-4 rounded-2xl bg-primary/10 w-fit">
                    {service.icon}
                  </div>
                </div>
                <h3 className="font-bold text-xl text-foreground mb-3">{service.title}</h3>
                <p className="text-muted-foreground mb-6">
                  {service.description}
                </p>
                <Button variant="outline" asChild>
                  <Link href={service.link}>
                    Learn More <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      {/* How It Works Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Simple, Fast, and Professional
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Getting high-quality prints has never been easier.
            </p>
          </div>
          
          <div className="relative grid gap-12 md:grid-cols-3">
            {/* Dashed line connector for desktop */}
            <div className="hidden md:block absolute top-1/2 left-0 w-full h-px -translate-y-1/2">
              <svg width="100%" height="2">
                <line x1="0" y1="1" x2="100%" y2="1" strokeWidth="2" strokeDasharray="8, 8" className="stroke-border" />
              </svg>
            </div>

            <div className="relative text-center z-10">
              <div className="mb-4 inline-block p-5 bg-background border-2 border-border rounded-full text-primary font-bold text-2xl">1</div>
              <h3 className="text-xl font-semibold mb-2">Upload Your Design</h3>
              <p className="text-muted-foreground">
                Easily upload your files or work with our team to create the perfect design.
              </p>
            </div>
            <div className="relative text-center z-10">
              <div className="mb-4 inline-block p-5 bg-background border-2 border-border rounded-full text-primary font-bold text-2xl">2</div>
              <h3 className="text-xl font-semibold mb-2">We Print & Perfect</h3>
              <p className="text-muted-foreground">
                Our experts use top-tier technology to ensure flawless quality and color accuracy.
              </p>
            </div>
            <div className="relative text-center z-10">
              <div className="mb-4 inline-block p-5 bg-background border-2 border-border rounded-full text-primary font-bold text-2xl">3</div>
              <h3 className="text-xl font-semibold mb-2">Delivered to Your Door</h3>
              <p className="text-muted-foreground">
                Fast, reliable shipping ensures your order arrives right when you need it.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 md:py-24 bg-surface">
        <div className="container mx-auto px-6">
          <div className="grid gap-12 md:grid-cols-2 items-center">
            <div className="relative h-[400px] md:h-[500px] rounded-3xl overflow-hidden shadow-xl animate-fade-in-scale">
              <Image 
                src="/hero-print-2.jpg" 
                alt="Close-up of high-quality paper texture" 
                fill 
                className="object-cover"
              />
            </div>
            <div className="space-y-8">
              <h2 className="text-3xl md:text-4xl font-bold text-foreground">
                The Multiprints Difference
              </h2>
              
              <div className="space-y-6">
                {whyChooseUs.map((item, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="flex-shrink-0 mt-1">
                      <CheckCircle className="h-6 w-6 text-success" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground">{item.title}</h3>
                      <p className="text-muted-foreground">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <Button size="lg" asChild className="btn-premium mt-6">
                <Link href="/about">
                  Learn More About Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
      
      {/* Final CTA Section */}
      <section className="py-20 md:py-32">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Ready to Bring Your Vision to Life?
            </h2>
            <p className="text-xl text-muted-foreground mb-10">
              Get a free, no-obligation quote for your next project. Our team is standing by to help you create something amazing.
            </p>
            <Button size="lg" asChild className="btn-premium px-10 py-8 text-xl rounded-2xl shadow-2xl hover:shadow-primary/40">
              <Link href="/contact">
                Get Your Free Quote Today <ArrowRight className="ml-3 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </PublicLayout>
  )
}
