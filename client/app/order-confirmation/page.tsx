import Layout from '../components/Layout.js';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';

export default function OrderConfirmationPage() {
  return (
    <Layout>
      <main className="container mx-auto px-6 py-16 md:py-24 text-center">
        <CheckCircle className="h-24 w-24 text-green-500 mx-auto mb-6" />
        <h1 className="text-4xl md:text-5xl font-extrabold text-foreground mb-4">
          Order Confirmed!
        </h1>
        <p className="text-lg text-muted-foreground mb-8">
          Thank you for your purchase. Your order has been successfully placed and will be processed shortly.
        </p>
        <div className="flex justify-center space-x-4">
          <Button asChild>
            <Link href="/">Continue Shopping</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/admin/dashboard">Go to Admin Dashboard</Link>
          </Button>
        </div>
      </main>
    </Layout>
  );
}
