'use client';

import Layout from '../../components/Layout.js';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { useCart } from '@/lib/context/CartContext';
import toast from 'react-hot-toast';

// Dummy data for products
const products = [
  {
    id: '1',
    name: 'Premium Business Cards',
    description: 'High-quality, custom-designed business cards to make a lasting impression. Our business cards are printed on premium stock with a variety of finishes including matte, gloss, and spot UV. Perfect for networking and professional branding.',
    price: 'Starting at $25.00',
    imageUrl: '/placeholder-business-cards.jpg',
    details: 'Available in various sizes and paper types. Fast turnaround times and competitive pricing. Design services also available.'
  },
  {
    id: '2',
    name: 'Large Format Banners',
    description: 'Durable, vibrant banners perfect for events, promotions, and outdoor advertising. Made from weather-resistant vinyl with reinforced grommets for easy hanging.',
    price: 'Starting at $75.00',
    imageUrl: '/placeholder-banners.jpg',
    details: 'Custom sizes available. Ideal for grand openings, trade shows, and outdoor events. UV-resistant inks ensure long-lasting color.'
  },
  {
    id: '3',
    name: 'Custom Mugs',
    description: 'Personalized mugs for gifts, promotions, or corporate branding. Dishwasher and microwave safe.',
    price: 'Starting at $10.00',
    imageUrl: '/placeholder-mugs.jpg',
    details: 'Choose from a wide range of colors and sizes. Perfect for corporate events, sports teams, and personal gifts. Minimum order quantities may apply.'
  },
  {
    id: '4',
    name: 'Brochures',
    description: 'Professionally printed brochures in various folds and sizes to effectively showcase your business.',
    price: 'Starting at $50.00',
    imageUrl: '/placeholder-brochures.jpg',
    details: 'Professional design services included. High-quality paper stock and vibrant printing to make your message stand out.'
  },
  {
    id: '5',
    name: 'Flyers',
    description: 'High-quality flyers for events, promotions, or informational handouts. Available in various paper stocks.',
    price: 'Starting at $0.50/flyer',
    imageUrl: '/placeholder-flyers.jpg',
    details: 'Reliable and trackable distribution. Ideal for local businesses, events, and promotions. Contact us for a custom quote.'
  },
  {
    id: '6',
    name: 'Posters',
    description: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.',
    price: 'Starting at $15.00',
    imageUrl: '/placeholder-posters.jpg',
    details: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.'
  },
];

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  const { id } = params;
  const product = products.find((item) => item.id === id);
  const { addToCart } = useCart();

  const handleAddToCart = () => {
    if (product) {
      addToCart(product);
      toast.success(`${product.name} added to cart!`);
    }
  };

  if (!product) {
    return (
      <Layout>
        <main className="container mx-auto px-6 py-16 md:py-24 text-center">
          <h1 className="text-4xl font-bold text-foreground mb-4">Product Not Found</h1>
          <p className="text-lg text-muted-foreground">The product you are looking for does not exist.</p>
          <Button asChild className="mt-8">
            <Link href="/products">Back to Products</Link>
          </Button>
        </main>
      </Layout>
    );
  }

  return (
    <Layout>
      <main className="container mx-auto px-6 py-16 md:py-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Product Image */}
          <div className="relative w-full h-96 rounded-lg overflow-hidden shadow-md">
            <Image
              src={product.imageUrl}
              alt={product.name}
              layout="fill"
              objectFit="cover"
              className="transition-transform duration-300 hover:scale-105"
            />
          </div>

          {/* Product Details */}
          <div className="flex flex-col justify-center">
            <h1 className="text-4xl md:text-5xl font-extrabold text-foreground mb-4">
              {product.name}
            </h1>
            <p className="text-2xl font-bold text-primary mb-6">
              {product.price}
            </p>
            <p className="text-lg text-muted-foreground mb-6">
              {product.description}
            </p>
            <p className="text-md text-muted-foreground mb-8">
              {product.details}
            </p>
            <Button onClick={handleAddToCart} className="w-full md:w-auto">
              Add to Cart
            </Button>
          </div>
        </div>
      </main>
    </Layout>
  );
}

