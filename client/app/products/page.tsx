'use client';

import React, { useState, useMemo } from 'react';
import { PublicLayout } from '@/components/layouts/public-layout';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import ProductQuickViewModal from '../components/ProductQuickViewModal.js';

// Dummy data for products
const productsData = [
  {
    id: '1',
    name: 'Premium Business Cards',
    description: 'High-quality, custom-designed business cards to make a lasting impression.',
    price: 25.00,
    imageUrl: '/placeholder-business-cards.jpg',
    inStock: true,
  },
  {
    id: '2',
    name: 'Large Format Banners',
    description: 'Durable, vibrant banners perfect for events, promotions, and outdoor advertising.',
    price: 75.00,
    imageUrl: '/placeholder-banners.jpg',
    inStock: true,
  },
  {
    id: '3',
    name: 'Custom Mugs',
    description: 'Personalized mugs for gifts, promotions, or corporate branding. Dishwasher and microwave safe.',
    price: 10.00,
    imageUrl: '/placeholder-mugs.jpg',
    inStock: false,
  },
  {
    id: '4',
    name: 'Brochures',
    description: 'Professionally printed brochures in various folds and sizes to effectively showcase your business.',
    price: 50.00,
    imageUrl: '/placeholder-brochures.jpg',
    inStock: true,
  },
  {
    id: '5',
    name: 'Flyers',
    description: 'High-quality flyers for events, promotions, or informational handouts. Available in various paper stocks.',
    price: 0.50,
    imageUrl: '/placeholder-flyers.jpg',
    inStock: true,
  },
  {
    id: '6',
    name: 'Posters',
    description: 'Vibrant posters for advertising, events, or decorative purposes. Available in a range of sizes.',
    price: 15.00,
    imageUrl: '/placeholder-posters.jpg',
    inStock: false,
  },
];

export default function ProductsPage() {
  const [sortBy, setSortBy] = useState('newest');
  const [availability, setAvailability] = useState('all');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');

  const filteredAndSortedProducts = useMemo(() => {
    let currentProducts = [...productsData];

    // Filter by availability
    if (availability === 'in-stock') {
      currentProducts = currentProducts.filter((p) => p.inStock);
    } else if (availability === 'out-of-stock') {
      currentProducts = currentProducts.filter((p) => !p.inStock);
    }

    // Filter by price range
    const min = parseFloat(minPrice);
    const max = parseFloat(maxPrice);

    if (!isNaN(min)) {
      currentProducts = currentProducts.filter((p) => p.price >= min);
    }
    if (!isNaN(max)) {
      currentProducts = currentProducts.filter((p) => p.price <= max);
    }

    // Sort products
    currentProducts.sort((a, b) => {
      switch (sortBy) {
        case 'price-asc':
          return a.price - b.price;
        case 'price-desc':
          return b.price - a.price;
        case 'name-asc':
          return a.name.localeCompare(b.name);
        case 'newest':
        default:
          return 0; // Or implement actual date sorting if dates were in dummy data
      }
    });

    return currentProducts;
  }, [sortBy, availability, minPrice, maxPrice]);

  return (
    <PublicLayout>
      <main className="container mx-auto px-6 py-16 md:py-24">
        <h1 className="text-4xl md:text-5xl font-extrabold text-left mb-12 text-foreground">
          Products
        </h1>

        {/* Filter and Sort Section - Moved to Top */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-8 space-y-4 md:space-y-0">
          {/* Sort By */}
          <div className="w-full md:w-1/3">
            <Card>
              <CardHeader className="py-2">
                <CardTitle className="text-base">Sort By</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select an option">{sortBy === 'newest' ? 'Newest' : sortBy === 'price-asc' ? 'Price: Low to High' : sortBy === 'price-desc' ? 'Price: High to Low' : sortBy === 'name-asc' ? 'Name (A-Z)' : 'Select an option'}</SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest</SelectItem>
                    <SelectItem value="price-asc">Price: Low to High</SelectItem>
                    <SelectItem value="price-desc">Price: High to Low</SelectItem>
                    <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>
          </div>

          {/* Availability and Price Range */}
          <div className="w-full md:w-2/3 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <Card className="w-full md:w-1/2">
              <CardHeader className="py-2">
                <CardTitle className="text-base">Availability</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <Select value={availability} onValueChange={setAvailability}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select availability">{availability === 'all' ? 'All' : availability === 'in-stock' ? 'In Stock' : availability === 'out-of-stock' ? 'Out of Stock' : 'Select availability'}</SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="in-stock">In Stock</SelectItem>
                    <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>

            <Card className="w-full md:w-1/2">
              <CardHeader className="py-2">
                <CardTitle className="text-base">Price Range</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="min-price" className="sr-only">Min Price</Label>
                  <Input id="min-price" type="number" placeholder="Min" className="w-1/2" value={minPrice} onChange={(e) => setMinPrice(e.target.value)} />
                  <span>-</span>
                  <Label htmlFor="max-price" className="sr-only">Max Price</Label>
                  <Input id="max-price" type="number" placeholder="Max" className="w-1/2" value={maxPrice} onChange={(e) => setMaxPrice(e.target.value)} />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {filteredAndSortedProducts.map((item) => (
            <Card key={item.id} className="flex flex-col overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all duration-300 ease-in-out group">
              <div className="relative w-full h-48 bg-muted overflow-hidden">
                <Image
                  src={item.imageUrl}
                  alt={item.name}
                  layout="fill"
                  objectFit="cover"
                  className="transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-xl font-semibold text-foreground leading-tight">
                  {item.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0 flex-grow">
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                  {item.description}
                </p>
                <p className="text-lg font-bold text-primary">
                  ${item.price.toFixed(2)}
                </p>
              </CardContent>
              <CardFooter className="p-4 pt-0">
                <ProductQuickViewModal product={item}>
                  <Button className="w-full">Buy now</Button>
                </ProductQuickViewModal>
              </CardFooter>
            </Card>
          ))}
        </div>
      </main>
    </PublicLayout>
  );
}
