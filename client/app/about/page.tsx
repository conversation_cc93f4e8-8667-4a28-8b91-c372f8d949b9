import { PublicLayout } from '@/components/layouts/public-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Lightbulb, Users } from 'lucide-react'; // Importing icons

export default function About() {
  return (
    <PublicLayout>
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center">
          <h1 className="text-lg font-semibold md:text-2xl">About Us</h1>
        </div>

        {/* Our Story Section */}
        <Card>
          <CardHeader>
            <CardTitle>Our Story</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Founded in 2010, Multiprints began with a simple vision: to provide high-quality, affordable printing solutions with exceptional customer service. What started as a small local print shop has grown into a comprehensive printing and design hub, serving businesses and individuals across the region.
            </p>
            <p className="text-muted-foreground">
              Over the years, we&apos;ve embraced technological advancements and expanded our services, but our core commitment to craftsmanship and client satisfaction has remained unwavering. We believe that every print project, big or small, deserves meticulous attention to detail and a flawless finish.
            </p>
          </CardContent>
        </Card>

        {/* Mission & Values Section */}
        <Card>
          <CardHeader>
            <CardTitle>Our Mission & Values</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Our mission is to empower our clients to communicate their message effectively through superior printing and innovative design. We strive to be a reliable partner, delivering outstanding results that exceed expectations and contribute to their success.
            </p>
            <ul className="list-disc list-inside text-muted-foreground space-y-2">
              <li><strong>Quality:</strong> Uncompromising standards in every print.</li>
              <li><strong>Integrity:</strong> Honest and transparent dealings.</li>
              <li><strong>Innovation:</strong> Embracing new technologies and creative solutions.</li>
              <li><strong>Customer Focus:</strong> Prioritizing client needs and satisfaction.</li>
              <li><strong>Sustainability:</strong> Responsible and eco-friendly practices.</li>
            </ul>
          </CardContent>
        </Card>

        {/* Why Choose Us Section */}
        <Card>
          <CardHeader>
            <CardTitle>Why Choose Multiprints?</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-primary mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-foreground">Unmatched Quality</h3>
                <p className="text-muted-foreground text-sm">We use state-of-the-art technology and premium materials for superior results.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Lightbulb className="h-6 w-6 text-primary mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-foreground">Innovative Solutions</h3>
                <p className="text-muted-foreground text-sm">Creative design and printing solutions tailored to your unique needs.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Users className="h-6 w-6 text-primary mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-foreground">Customer-Centric Service</h3>
                <p className="text-muted-foreground text-sm">Dedicated support and personalized attention from start to finish.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-primary mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-foreground">Timely Delivery</h3>
                <p className="text-muted-foreground text-sm">Efficient processes ensure your projects are completed on schedule.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Section (Placeholder) */}
        <Card>
          <CardHeader>
            <CardTitle>Meet Our Team</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Our dedicated team of printing specialists, designers, and customer service professionals are passionate about bringing your ideas to life. We combine expertise with a friendly approach to ensure a seamless experience.
            </p>
            {/* You can add team member cards here if desired */}
          </CardContent>
        </Card>
      </main>
    </PublicLayout>
  );
}
