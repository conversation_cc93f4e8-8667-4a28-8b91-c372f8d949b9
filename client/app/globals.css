@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Premium Professional Design System for Printing Business */

    /* Core Background & Surface Colors */
    --background: 0 0% 100%;
    --surface: 0 0% 99%;
    --surface-elevated: 0 0% 100%;
    --foreground: 215 25% 8%;

    /* Card & Container Colors */
    --card: 0 0% 100%;
    --card-foreground: 215 25% 8%;
    --card-elevated: 0 0% 100%;

    /* Popover & Modal Colors */
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 8%;

    /* Premium Brand Colors - Deep Navy & Gold Accent */
    --primary: 215 84% 12%;          /* Deep Navy Blue - Professional & Trustworthy */
    --primary-foreground: 0 0% 100%;
    --primary-hover: 215 84% 8%;
    --primary-light: 215 84% 97%;
    --primary-50: 215 84% 95%;
    --primary-100: 215 84% 90%;
    --primary-200: 215 84% 80%;

    /* Sophisticated Secondary Colors */
    --secondary: 210 17% 96%;
    --secondary-foreground: 215 25% 15%;
    --secondary-hover: 210 17% 92%;
    --secondary-dark: 210 17% 88%;

    /* Premium Accent - Warm Gold */
    --accent: 45 100% 51%;           /* Professional Gold */
    --accent-foreground: 215 25% 8%;
    --accent-hover: 45 100% 47%;
    --accent-light: 45 100% 95%;

    /* Refined Neutral Palette */
    --muted: 210 17% 96%;
    --muted-foreground: 215 16% 46%;
    --muted-dark: 210 17% 88%;

    /* Professional Status Colors */
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;
    --destructive-light: 0 72% 95%;

    --success: 142 69% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 69% 95%;

    --warning: 38 92% 50%;
    --warning-foreground: 215 25% 8%;
    --warning-light: 38 92% 95%;

    --info: 199 89% 48%;
    --info-foreground: 0 0% 100%;
    --info-light: 199 89% 95%;

    /* Refined UI Elements */
    --border: 210 17% 90%;
    --border-light: 210 17% 94%;
    --border-strong: 210 17% 80%;
    --input: 210 17% 96%;
    --ring: 215 84% 12%;

    /* Modern Border Radius System */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Professional Shadow System */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Premium Gradient System */
    --gradient-primary: linear-gradient(135deg, hsl(215 84% 12%) 0%, hsl(215 84% 8%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(45 100% 51%) 0%, hsl(45 100% 47%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(215 84% 12%) 0%, hsl(215 84% 16%) 50%, hsl(45 100% 51%) 100%);
    --gradient-surface: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 17% 98%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 17% 99%) 100%);

    /* Chart & Data Visualization */
    --chart-1: 215 84% 12%;
    --chart-2: 45 100% 51%;
    --chart-3: 142 69% 45%;
    --chart-4: 199 89% 48%;
    --chart-5: 0 72% 51%;
  }

  .dark {
    /* Dark mode colors */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 250 91% 65%;
    --secondary-foreground: 0 0% 100%;
    
    --accent: 340 82% 52%;
    --accent-foreground: 0 0% 100%;
    
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 98%;
    
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;
    
    --info: 200 98% 39%;
    --info-foreground: 0 0% 100%;
    
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 217 91% 60%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold;
  }

  h3 {
    @apply text-2xl md:text-3xl font-semibold;
  }

  h4 {
    @apply text-xl md:text-2xl font-semibold;
  }

  h5 {
    @apply text-lg md:text-xl font-medium;
  }

  h6 {
    @apply text-base md:text-lg font-medium;
  }

  p {
    @apply leading-relaxed;
  }

  .text-balance {
    text-wrap: balance;
  }
}

/* Modern animations and transitions */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Animation utility classes */
.animate-fade-in-up {
  animation: fade-in-up 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Animation delays */
.animation-delay-100 { animation-delay: 0.1s; }
.animation-delay-200 { animation-delay: 0.2s; }
.animation-delay-300 { animation-delay: 0.3s; }
.animation-delay-400 { animation-delay: 0.4s; }
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-600 { animation-delay: 0.6s; }

/* Modern utility classes */
.glass-effect {
  @apply bg-white/80 dark:bg-black/20 backdrop-blur-md border border-white/30 dark:border-white/10;
}

/* Professional Gradient Backgrounds */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-surface);
}

.gradient-accent {
  background: var(--gradient-accent);
}

.gradient-hero {
  background: var(--gradient-hero);
}

.gradient-card {
  background: var(--gradient-card);
}

/* Professional Text Gradients */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-accent {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern shadows */
.shadow-card {
  @apply shadow-lg shadow-gray-200/50 dark:shadow-gray-900/30;
}

.shadow-card-hover {
  @apply shadow-xl shadow-gray-200/50 dark:shadow-gray-900/30;
}

.shadow-button {
  @apply shadow-md shadow-primary-500/20 dark:shadow-primary-500/10;
}

/* Hover effects */
.hover-lift {
  @apply transition-all duration-300 ease-out;
}

.hover-lift:hover {
  @apply -translate-y-1 shadow-card-hover;
}

.hover-scale {
  @apply transition-all duration-300 ease-out;
}

.hover-scale:hover {
  @apply scale-105;
}

/* Mobile-friendly utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly sizing for mobile */
@media (max-width: 768px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Layout utilities */
.layout-container {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 md:py-16 lg:py-24;
}

/* Card styles */
.card-interactive {
  @apply bg-card rounded-xl border border-border p-6 shadow-card transition-all duration-300 hover:shadow-card-hover hover:-translate-y-1;
}

.card-static {
  @apply bg-card rounded-xl border border-border p-6 shadow-card;
}

/* Button styles */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary-600 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary-600 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

.btn-outline {
  @apply border border-input bg-background hover:bg-muted hover:text-foreground inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

/* Status badges */
.badge {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}

.badge-primary {
  @apply bg-primary/10 text-primary-700 dark:bg-primary/20 dark:text-primary-300;
}

.badge-secondary {
  @apply bg-secondary/10 text-secondary-700 dark:bg-secondary/20 dark:text-secondary-300;
}

.badge-success {
  @apply bg-success/10 text-success-700 dark:bg-success/20 dark:text-success-300;
}

.badge-warning {
  @apply bg-warning/10 text-warning-700 dark:bg-warning/20 dark:text-warning-300;
}

.badge-error {
  @apply bg-error/10 text-error-700 dark:bg-error/20 dark:text-error-300;
}

/* Form elements */
.form-input {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-select {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-checkbox {
  @apply h-4 w-4 rounded border border-input bg-background text-primary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-radio {
  @apply h-4 w-4 rounded-full border border-input bg-background text-primary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

/* Admin dashboard specific styles */
.admin-layout {
  @apply grid min-h-screen w-full md:grid-cols-[280px_1fr] lg:grid-cols-[300px_1fr];
}

.admin-sidebar {
  @apply fixed inset-y-0 left-0 z-20 hidden w-[280px] border-r bg-card/95 backdrop-blur-md md:block lg:w-[300px];
}

/* Professional Design System Utilities */

/* Premium Card Effects */
.card-premium {
  background: var(--gradient-card);
  border: 1px solid hsl(var(--border-light));
  box-shadow: var(--shadow-md);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-premium:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: hsl(var(--primary) / 0.2);
}

/* Professional Button Effects */
.btn-premium {
  background: var(--gradient-primary);
  color: hsl(var(--primary-foreground));
  border: none;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-premium:hover::before {
  left: 100%;
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Professional Trust Elements */
.trust-badge-premium {
  background: var(--gradient-primary);
  color: hsl(var(--primary-foreground));
  padding: 0.5rem 1rem;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-md);
  border: 1px solid hsl(var(--primary) / 0.2);
}

/* Professional Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-success {
  background: hsl(var(--success-light));
  color: hsl(var(--success));
  border: 1px solid hsl(var(--success) / 0.2);
}

/* Professional Hover Effects */
.hover-lift-premium {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift-premium:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Professional Loading States */
.loading-shimmer {
  background: linear-gradient(90deg,
    hsl(var(--muted)) 25%,
    hsl(var(--muted-dark)) 50%,
    hsl(var(--muted)) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.admin-main {
  @apply flex flex-col min-h-screen;
}

.admin-header {
  @apply sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background/95 px-6 backdrop-blur-md;
}

.admin-content {
  @apply flex-1 p-6 md:p-8;
}

/* Status indicators */
.status-indicator {
  @apply relative flex h-2.5 w-2.5 rounded-full;
}

.status-indicator::before {
  content: '';
  @apply absolute inset-0 rounded-full animate-ping;
}

.status-indicator-online {
  @apply bg-success-500;
}

.status-indicator-online::before {
  @apply bg-success-500/50;
}

.status-indicator-away {
  @apply bg-warning-500;
}

.status-indicator-away::before {
  @apply bg-warning-500/50;
}

.status-indicator-offline {
  @apply bg-gray-400;
}

/* Print-specific styles */
.print-preview {
  @apply border border-dashed border-border p-4 rounded-lg bg-background shadow-inner;
}

.print-bleed-marks {
  @apply border border-dashed;
  border-color: hsl(var(--destructive));
}

.print-safe-area {
  @apply border border-dashed;
  border-color: hsl(var(--success));
}