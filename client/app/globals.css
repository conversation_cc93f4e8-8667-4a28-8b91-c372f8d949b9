@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220 8.9% 46.1%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220 8.9% 46.1%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 14.3% 95.9%;
    --input: 220 14.3% 95.9%;
    --ring: 262 83% 58%;
    --radius: 0.5rem;
    --chart-bar-fill: 262 83% 58%; /* Light mode chart bar color */
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 210 20% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 210 20% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 210 20% 98%;
    --primary: 262 83% 58%; /* Keeping light mode primary */
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 14.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 220 14.3% 14.9%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 220 14.3% 14.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 14.3% 14.9%;
    --input: 220 14.3% 14.9%;
    --ring: 262 83% 58%;
    --chart-bar-fill: 210 80% 70%; /* Dark mode chart bar color */
  }

  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

.animation-delay-600 {
    .animation-delay-600 {
    animation-delay: 0.6s;
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-scroll-left {
  animation: scroll-left 20s linear infinite;
}