@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;
    
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 250 91% 65%;
    --secondary-foreground: 0 0% 100%;
    
    --accent: 340 82% 52%;
    --accent-foreground: 0 0% 100%;
    
    --muted: 210 40% 98%;
    --muted-foreground: 215 16% 46%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;
    
    --info: 200 98% 39%;
    --info-foreground: 0 0% 100%;
    
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 217 91% 60%;
    
    --radius: 0.75rem;
  }

  .dark {
    /* Dark mode colors */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 250 91% 65%;
    --secondary-foreground: 0 0% 100%;
    
    --accent: 340 82% 52%;
    --accent-foreground: 0 0% 100%;
    
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 98%;
    
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;
    
    --info: 200 98% 39%;
    --info-foreground: 0 0% 100%;
    
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 217 91% 60%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold;
  }

  h3 {
    @apply text-2xl md:text-3xl font-semibold;
  }

  h4 {
    @apply text-xl md:text-2xl font-semibold;
  }

  h5 {
    @apply text-lg md:text-xl font-medium;
  }

  h6 {
    @apply text-base md:text-lg font-medium;
  }

  p {
    @apply leading-relaxed;
  }

  .text-balance {
    text-wrap: balance;
  }
}

/* Modern animations and transitions */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Animation utility classes */
.animate-fade-in-up {
  animation: fade-in-up 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Animation delays */
.animation-delay-100 { animation-delay: 0.1s; }
.animation-delay-200 { animation-delay: 0.2s; }
.animation-delay-300 { animation-delay: 0.3s; }
.animation-delay-400 { animation-delay: 0.4s; }
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-600 { animation-delay: 0.6s; }

/* Modern utility classes */
.glass-effect {
  @apply bg-white/80 dark:bg-black/20 backdrop-blur-md border border-white/30 dark:border-white/10;
}

/* Gradient backgrounds */
.gradient-primary {
  @apply bg-gradient-to-r from-primary-600 to-primary-500;
}

.gradient-secondary {
  @apply bg-gradient-to-r from-secondary-600 to-secondary-500;
}

.gradient-accent {
  @apply bg-gradient-to-r from-accent-600 to-accent-500;
}

.gradient-hero {
  @apply bg-gradient-to-br from-primary-500 via-secondary-500 to-accent-500;
}

/* Text gradients */
.text-gradient-primary {
  @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
}

.text-gradient-secondary {
  @apply bg-gradient-to-r from-secondary-600 to-secondary-400 bg-clip-text text-transparent;
}

.text-gradient-accent {
  @apply bg-gradient-to-r from-accent-600 to-accent-400 bg-clip-text text-transparent;
}

/* Modern shadows */
.shadow-card {
  @apply shadow-lg shadow-gray-200/50 dark:shadow-gray-900/30;
}

.shadow-card-hover {
  @apply shadow-xl shadow-gray-200/50 dark:shadow-gray-900/30;
}

.shadow-button {
  @apply shadow-md shadow-primary-500/20 dark:shadow-primary-500/10;
}

/* Hover effects */
.hover-lift {
  @apply transition-all duration-300 ease-out;
}

.hover-lift:hover {
  @apply -translate-y-1 shadow-card-hover;
}

.hover-scale {
  @apply transition-all duration-300 ease-out;
}

.hover-scale:hover {
  @apply scale-105;
}

/* Mobile-friendly utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly sizing for mobile */
@media (max-width: 768px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Layout utilities */
.layout-container {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 md:py-16 lg:py-24;
}

/* Card styles */
.card-interactive {
  @apply bg-card rounded-xl border border-border p-6 shadow-card transition-all duration-300 hover:shadow-card-hover hover:-translate-y-1;
}

.card-static {
  @apply bg-card rounded-xl border border-border p-6 shadow-card;
}

/* Button styles */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary-600 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary-600 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

.btn-outline {
  @apply border border-input bg-background hover:bg-muted hover:text-foreground inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
}

/* Status badges */
.badge {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}

.badge-primary {
  @apply bg-primary/10 text-primary-700 dark:bg-primary/20 dark:text-primary-300;
}

.badge-secondary {
  @apply bg-secondary/10 text-secondary-700 dark:bg-secondary/20 dark:text-secondary-300;
}

.badge-success {
  @apply bg-success/10 text-success-700 dark:bg-success/20 dark:text-success-300;
}

.badge-warning {
  @apply bg-warning/10 text-warning-700 dark:bg-warning/20 dark:text-warning-300;
}

.badge-error {
  @apply bg-error/10 text-error-700 dark:bg-error/20 dark:text-error-300;
}

/* Form elements */
.form-input {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-select {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-checkbox {
  @apply h-4 w-4 rounded border border-input bg-background text-primary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

.form-radio {
  @apply h-4 w-4 rounded-full border border-input bg-background text-primary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

/* Admin dashboard specific styles */
.admin-layout {
  @apply grid min-h-screen w-full md:grid-cols-[280px_1fr] lg:grid-cols-[300px_1fr];
}

.admin-sidebar {
  @apply fixed inset-y-0 left-0 z-20 hidden w-[280px] border-r bg-card/95 backdrop-blur-md md:block lg:w-[300px];
}

.admin-main {
  @apply flex flex-col min-h-screen;
}

.admin-header {
  @apply sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background/95 px-6 backdrop-blur-md;
}

.admin-content {
  @apply flex-1 p-6 md:p-8;
}

/* Status indicators */
.status-indicator {
  @apply relative flex h-2.5 w-2.5 rounded-full;
}

.status-indicator::before {
  content: '';
  @apply absolute inset-0 rounded-full animate-ping;
}

.status-indicator-online {
  @apply bg-success-500;
}

.status-indicator-online::before {
  @apply bg-success-500/50;
}

.status-indicator-away {
  @apply bg-warning-500;
}

.status-indicator-away::before {
  @apply bg-warning-500/50;
}

.status-indicator-offline {
  @apply bg-gray-400;
}

/* Print-specific styles */
.print-preview {
  @apply border border-dashed border-gray-300 p-4 rounded-lg bg-white shadow-inner;
}

.print-bleed-marks {
  @apply border border-red-500 border-dashed;
}

.print-safe-area {
  @apply border border-green-500 border-dashed;
}