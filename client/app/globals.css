@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern sophisticated color palette */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Premium gradient-based primary colors */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 221 83% 48%;
    --primary-light: 221 83% 95%;

    /* Sophisticated secondary palette */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    --secondary-hover: 210 40% 92%;

    /* Enhanced neutral colors */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    /* Status colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;

    /* UI elements */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    --radius: 0.75rem;

    /* Chart colors */
    --chart-bar-fill: 221 83% 53%;

    /* Gradient variables */
    --gradient-primary: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(221 83% 48%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(210 40% 96%) 0%, hsl(210 40% 92%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(262 83% 58%) 100%);
  }

  .dark {
    /* Dark mode sophisticated palette */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Enhanced primary colors for dark mode */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 221 83% 58%;
    --primary-light: 221 83% 15%;

    /* Dark mode secondary palette */
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;
    --secondary-hover: 217 32% 22%;

    /* Dark mode neutrals */
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 32% 17%;
    --accent-foreground: 210 40% 98%;

    /* Dark mode status colors */
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;

    /* Dark mode UI elements */
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 221 83% 53%;

    /* Dark mode chart colors */
    --chart-bar-fill: 221 83% 53%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(221 83% 58%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(217 32% 17%) 0%, hsl(217 32% 22%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(262 83% 58%) 100%);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
    font-weight: 700;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    font-weight: 600;
  }

  h3 {
    @apply text-2xl md:text-3xl;
    font-weight: 600;
  }

  h4 {
    @apply text-xl md:text-2xl;
    font-weight: 600;
  }

  p {
    @apply leading-relaxed;
  }

  .text-balance {
    text-wrap: balance;
  }
}

/* Modern animations and transitions */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Animation utility classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Animation delays */
.animation-delay-100 { animation-delay: 0.1s; }
.animation-delay-200 { animation-delay: 0.2s; }
.animation-delay-300 { animation-delay: 0.3s; }
.animation-delay-400 { animation-delay: 0.4s; }
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-600 { animation-delay: 0.6s; }

/* Modern utility classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-hero {
  background: var(--gradient-hero);
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-modern {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-modern-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-modern-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.08);
}