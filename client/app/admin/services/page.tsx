"use client"

import toast from "react-hot-toast";
import { Sidebar } from "@/components/sidebar"
import { Head<PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { PlusCircle, Upload } from "lucide-react"
import Image from "next/image"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetTrigger, SheetFooter } from "@/components/ui/sheet"
import React, { useState } from "react"

// Dummy data for services
const initialServices = [
  {
    id: "1",
    image: "/placeholder.svg", // Placeholder image
    name: "Custom Apparel Printing",
    description: "High-quality custom apparel printing for all occasions.",
    price: "$15.00",
    dateAdded: "2023-01-15",
  },
  {
    id: "2",
    image: "/placeholder.svg",
    name: "Large Format Design",
    description: "Professional large format design services for events and businesses.",
    price: "$50.00",
    dateAdded: "2023-03-10",
  },
  {
    id: "3",
    image: "/placeholder.svg",
    name: "Brand Identity Creation",
    description: "Unique and memorable brand identity designs for your business.",
    price: "N/A",
    dateAdded: "2023-04-01",
  },
];

export default function ServicesPage() {
  const [services, setServices] = useState(initialServices);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [editingService, setEditingService] = useState<any | null>(null);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      setImagePreviewUrl(URL.createObjectURL(file));
    } else {
      setImageFile(null);
      setImagePreviewUrl(null);
    }
  };

  const handleEditClick = (service: any) => {
    setEditingService(service);
    setIsEditSheetOpen(true);
    setImagePreviewUrl(service.image); // Set existing image for preview
  };

  const handleSaveEdit = () => {
    // Placeholder for saving edited service data
    console.log("Saving edited service:", editingService);
    setServices(services.map(s => s.id === editingService.id ? editingService : s));
    setIsEditSheetOpen(false);
    setEditingService(null);
    setImageFile(null);
    setImagePreviewUrl(null);
    toast.success("Service updated successfully!");
  };

  const handleDeleteService = (id: string) => {
    if (window.confirm("Are you sure you want to delete this service?")) {
      setServices(services.filter(s => s.id !== id));
      toast.success("Service deleted successfully!");
    }
  };

  return (
    <div className="min-h-screen md:pl-64 lg:pl-72">
      <Sidebar />
      <div className="flex flex-col">
        <Header />
        <main className="grid flex-1 items-start gap-4 p-4 sm:px-6 sm:pt-4 md:gap-6">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold md:text-2xl">Services</h1>
            <div className="ml-auto flex items-center gap-2">
              {/* Add Service Sheet */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button size="sm" className="h-8 gap-1">
                    <PlusCircle className="h-3.5 w-3.5" />
                    <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                      Add Service
                    </span>
                  </Button>
                </SheetTrigger>
                <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Add New Service</SheetTitle>
                    <SheetDescription>
                      Fill in the details to add a new service.
                    </SheetDescription>
                  </SheetHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-3">
                      <Label htmlFor="name">Service Name</Label>
                      <Input id="name" type="text" className="w-full" defaultValue="" />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="description">Description</Label>
                      <Textarea id="description" defaultValue="" className="min-h-32" />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="price">Price (Optional)</Label>
                      <Input id="price" type="text" className="w-full" defaultValue="" placeholder="e.g., $15.00 or N/A" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="image">Service Image</Label>
                      <div className="flex items-center justify-center rounded-md border border-dashed p-4">
                        {imagePreviewUrl ? (
                          <Image
                            src={imagePreviewUrl}
                            alt="Image Preview"
                            width={100}
                            height={100}
                            className="object-cover rounded-md"
                          />
                        ) : (
                          <div className="flex flex-col items-center gap-2">
                            <Upload className="h-8 w-8 text-muted-foreground" />
                            <span className="text-sm text-muted-foreground">Drag and drop or click to upload</span>
                          </div>
                        )}
                        <Input id="image" type="file" className="sr-only" onChange={handleImageChange} />
                      </div>
                    </div>
                  </div>
                  <SheetFooter>
                    <Button type="submit">Save Service</Button>
                  </SheetFooter>
                </SheetContent>
              </Sheet>
            </div>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Services</CardTitle>
              <CardDescription>
                Manage your printing services.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="hidden w-[100px] sm:table-cell">
                      <span className="sr-only">Image</span>
                    </TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead className="hidden md:table-cell">
                      Date Added
                    </TableHead>
                    <TableHead>
                      <span className="sr-only">Actions</span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services.map((service) => (
                    <TableRow key={service.id}>
                      <TableCell className="hidden sm:table-cell">
                        <Image
                          alt="Service image"
                          className="aspect-square rounded-md object-cover"
                          height="64"
                          src={service.image}
                          width="64"
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        {service.name}
                      </TableCell>
                      <TableCell>
                        {service.description}
                      </TableCell>
                      <TableCell>
                        {service.price}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {service.dateAdded}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => handleEditClick(service)}>Edit</Button>
                        <Button variant="destructive" size="sm" className="ml-2">Delete</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
        {/* Edit Service Sheet */}
        <Sheet open={isEditSheetOpen} onOpenChange={setIsEditSheetOpen}>
          <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
            <SheetHeader>
              <SheetTitle>Edit Service</SheetTitle>
              <SheetDescription>
                Make changes to the service details.
              </SheetDescription>
            </SheetHeader>
            {editingService && (
              <div className="grid gap-4 py-4">
                <div className="grid gap-3">
                  <Label htmlFor="edit-name">Service Name</Label>
                  <Input
                    id="edit-name"
                    type="text"
                    className="w-full"
                    defaultValue={editingService.name}
                    onChange={(e) =>
                      setEditingService({ ...editingService, name: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    defaultValue={editingService.description}
                    className="min-h-32"
                    onChange={(e) =>
                      setEditingService({ ...editingService, description: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="edit-price">Price (Optional)</Label>
                  <Input
                    id="edit-price"
                    type="text"
                    className="w-full"
                    defaultValue={editingService.price}
                    placeholder="e.g., $15.00 or N/A"
                    onChange={(e) =>
                      setEditingService({ ...editingService, price: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-image">Service Image</Label>
                  <div className="flex items-center justify-center rounded-md border border-dashed p-4">
                    {imagePreviewUrl ? (
                      <Image
                        src={imagePreviewUrl}
                        alt="Image Preview"
                        width={100}
                        height={100}
                        className="object-cover rounded-md"
                      />
                    ) : (
                      <div className="flex flex-col items-center gap-2">
                        <Upload className="h-8 w-8 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Drag and drop or click to upload</span>
                      </div>
                    )}
                    <Input id="edit-image" type="file" className="sr-only" onChange={handleImageChange} />
                  </div>
                </div>
              </div>
            )}
            <SheetFooter>
              <Button type="submit" onClick={handleSaveEdit}>Save Changes</Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>
        <Button className="fixed bottom-4 right-4 h-16 w-16 rounded-full shadow-lg md:bottom-8 md:right-8">
          <PlusCircle className="h-8 w-8" />
          <span className="sr-only">Add Service</span>
        </Button>
      </div>
    </div>
  )
}
