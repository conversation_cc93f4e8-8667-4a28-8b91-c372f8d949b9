"use client"

import toast from "react-hot-toast";
import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusCircle, Upload } from "lucide-react"
import Image from "next/image"
import React, { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetTrigger, SheetFooter } from "@/components/ui/sheet"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Dummy data for products
const initialProducts = [
  {
    id: "1",
    image: "/placeholder.svg", // Placeholder image
    name: "Premium Business Cards",
    category: "Product",
    price: "$25.00",
    dateAdded: "2023-01-15",
  },
  {
    id: "2",
    image: "/placeholder.svg",
    name: "Custom Mugs",
    category: "Product",
    price: "$10.00",
    dateAdded: "2023-02-01",
  },
  {
    id: "3",
    image: "/placeholder.svg",
    name: "Promotional Pens",
    category: "Product",
    price: "$2.00",
    dateAdded: "2023-03-10",
  },
];

export default function ProductsPage() {
  const [products, setProducts] = useState(initialProducts);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [editingProduct, setEditingProduct] = useState<any | null>(null);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      setImagePreviewUrl(URL.createObjectURL(file));
    } else {
      setImageFile(null);
      setImagePreviewUrl(null);
    }
  };

  const handleEditClick = (product: any) => {
    setEditingProduct(product);
    setIsEditSheetOpen(true);
    setImagePreviewUrl(product.image); // Set existing image for preview
  };

  const handleSaveEdit = () => {
    // Placeholder for saving edited product data
    console.log("Saving edited product:", editingProduct);
    setProducts(products.map(p => p.id === editingProduct.id ? editingProduct : p));
    setIsEditSheetOpen(false);
    setEditingProduct(null);
    setImageFile(null);
    setImagePreviewUrl(null);
    toast.success("Product updated successfully!");
  };

  const handleDeleteProduct = (id: string) => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      setProducts(products.filter(p => p.id !== id));
      toast.success("Product deleted successfully!");
    }
  };

  return (
    <div className="min-h-screen w-full md:pl-64 lg:pl-72">
      <Sidebar />
      <div className="flex flex-col">
        <Header />
        <main className="grid flex-1 items-start gap-4 p-4 sm:px-6 sm:pt-4 md:gap-6">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold md:text-2xl">Products</h1>
            <div className="ml-auto flex items-center gap-2">
              {/* Add Product Sheet */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button size="sm" className="h-8 gap-1">
                    <PlusCircle className="h-3.5 w-3.5" />
                    <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                      Add Product
                    </span>
                  </Button>
                </SheetTrigger>
                <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Add New Product</SheetTitle>
                    <SheetDescription>
                      Fill in the details to add a new product or service.
                    </SheetDescription>
                  </SheetHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-3">
                      <Label htmlFor="name">Name</Label>
                      <Input id="name" type="text" className="w-full" defaultValue="" />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="description">Description</Label>
                      <Textarea id="description" defaultValue="" className="min-h-32" />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="category">Category</Label>
                      <Select>
                        <SelectTrigger id="category" aria-label="Select category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="product">Product</SelectItem>
                          <SelectItem value="service">Service</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="price">Price</Label>
                      <Input id="price" type="number" className="w-full" defaultValue="0.00" />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="image">Product Image</Label>
                      <div className="flex items-center justify-center rounded-md border border-dashed p-4">
                        {imagePreviewUrl ? (
                          <Image
                            src={imagePreviewUrl}
                            alt="Image Preview"
                            width={100}
                            height={100}
                            className="object-cover rounded-md"
                          />
                        ) : (
                          <div className="flex flex-col items-center gap-2">
                            <Upload className="h-8 w-8 text-muted-foreground" />
                            <span className="text-sm text-muted-foreground">Drag and drop or click to upload</span>
                          </div>
                        )}
                        <Input id="image" type="file" className="sr-only" onChange={handleImageChange} />
                      </div>
                    </div>
                  </div>
                  <SheetFooter>
                    <Button type="submit">Save Product</Button>
                  </SheetFooter>
                </SheetContent>
              </Sheet>
            </div>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Products</CardTitle>
              <CardDescription>
                Manage your products and services.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="hidden w-[100px] sm:table-cell">
                      <span className="sr-only">Image</span>
                    </TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead className="hidden md:table-cell">
                      Date Added
                    </TableHead>
                    <TableHead>
                      <span className="sr-only">Actions</span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="hidden sm:table-cell">
                        <Image
                          alt="Product image"
                          className="aspect-square rounded-md object-cover"
                          height="64"
                          src={product.image}
                          width="64"
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        {product.name}
                      </TableCell>
                      <TableCell>
                        {product.category}
                      </TableCell>
                      <TableCell>
                        {product.price}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {product.dateAdded}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => handleEditClick(product)}>Edit</Button>
                        <Button variant="destructive" size="sm" className="ml-2">Delete</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
        {/* Edit Product Sheet */}
        <Sheet open={isEditSheetOpen} onOpenChange={setIsEditSheetOpen}>
          <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
            <SheetHeader>
              <SheetTitle>Edit Product</SheetTitle>
              <SheetDescription>
                Make changes to the product details.
              </SheetDescription>
            </SheetHeader>
            {editingProduct && (
              <div className="grid gap-4 py-4">
                <div className="grid gap-3">
                  <Label htmlFor="edit-name">Name</Label>
                  <Input
                    id="edit-name"
                    type="text"
                    className="w-full"
                    defaultValue={editingProduct.name}
                    onChange={(e) =>
                      setEditingProduct({ ...editingProduct, name: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    defaultValue={editingProduct.description}
                    className="min-h-32"
                    onChange={(e) =>
                      setEditingProduct({ ...editingProduct, description: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="edit-category">Category</Label>
                  <Select
                    defaultValue={editingProduct.category}
                    onValueChange={(value) =>
                      setEditingProduct({ ...editingProduct, category: value })
                    }
                  >
                    <SelectTrigger id="edit-category" aria-label="Select category">
                      <SelectValue placeholder="Select category">{editingProduct.category}</SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="product">Product</SelectItem>
                      <SelectItem value="service">Service</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="edit-price">Price</Label>
                  <Input
                    id="edit-price"
                    type="number"
                    className="w-full"
                    defaultValue={editingProduct.price.replace('$', '').replace(',', '')}
                    onChange={(e) =>
                      setEditingProduct({ ...editingProduct, price: `${e.target.value}` })
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-image">Product Image</Label>
                  <div className="flex items-center justify-center rounded-md border border-dashed p-4">
                    {imagePreviewUrl ? (
                      <Image
                        src={imagePreviewUrl}
                        alt="Image Preview"
                        width={100}
                        height={100}
                        className="object-cover rounded-md"
                      />
                    ) : (
                      <div className="flex flex-col items-center gap-2">
                        <Upload className="h-8 w-8 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Drag and drop or click to upload</span>
                      </div>
                    )}
                    <Input id="edit-image" type="file" className="sr-only" onChange={handleImageChange} />
                  </div>
                </div>
              </div>
            )}
            <SheetFooter>
              <Button type="submit" onClick={handleSaveEdit}>Save Changes</Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>
        <Button className="fixed bottom-4 right-4 h-16 w-16 rounded-full shadow-lg md:bottom-8 md:right-8">
          <PlusCircle className="h-8 w-8" />
          <span className="sr-only">Add Product</span>
        </Button>
      </div>
    </div>
  )
}