"use client"

import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import toast from "react-hot-toast";
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import React, { useState } from "react"

// Dummy data for payment history
const paymentHistory = [
  {
    id: "1",
    amount: "$150.00",
    customer: "<PERSON>",
    method: "Mpesa STK Push",
    status: "Success",
    date: "2024-07-10 10:30 AM",
  },
  {
    id: "2",
    amount: "$75.00",
    customer: "<PERSON>",
    method: "Manual - Cash",
    status: "Success",
    date: "2024-07-09 03:00 PM",
  },
  {
    id: "3",
    amount: "$200.00",
    customer: "Peter Jones",
    method: "Mpesa STK Push",
    status: "Pending",
    date: "2024-07-09 11:00 AM",
  },
];

export default function PaymentsPage() {
  const [manualAmount, setManualAmount] = useState("");
  const [manualCustomer, setManualCustomer] = useState("");
  const [manualMethod, setManualMethod] = useState("");
  const [manualNotes, setManualNotes] = useState("");

  const [mpesaPhone, setMpesaPhone] = useState("");
  const [mpesaAmount, setMpesaAmount] = useState("");

  const handleManualPayment = () => {
    console.log("Manual Payment:", { manualAmount, manualCustomer, manualMethod, manualNotes });
    // Add logic to save manual payment
    // For now, just clear the form
    setManualAmount("");
    setManualCustomer("");
    setManualMethod("");
    setManualNotes("");
    toast.success("Manual payment recorded!");
  };

  const handleMpesaSTKPush = () => {
    console.log("Mpesa STK Push Request:", { mpesaPhone, mpesaAmount });
    // Add logic to request Mpesa STK Push
    // For now, just clear the form
    setMpesaPhone("");
    setMpesaAmount("");
    toast.success("Mpesa STK Push requested!");
  };

  return (
    <div className="min-h-screen md:pl-64 lg:pl-72">
      <Sidebar />
      <div className="flex flex-col">
        <Header />
        <main className="grid flex-1 items-start gap-4 p-4 sm:px-6 sm:pt-4 md:gap-4">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold md:text-2xl">Payments</h1>
          </div>

          <Tabs defaultValue="manual" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="manual">Manual Payment</TabsTrigger>
              <TabsTrigger value="mpesa">Mpesa STK Push</TabsTrigger>
            </TabsList>
            <TabsContent value="manual">
              {/* Manual Payment Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Manual Payment</CardTitle>
                  <CardDescription>Record a payment manually.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="manual-amount">Amount</Label>
                      <Input
                        id="manual-amount"
                        type="number"
                        value={manualAmount}
                        onChange={(e) => setManualAmount(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="manual-customer">Customer Name</Label>
                      <Input
                        id="manual-customer"
                        type="text"
                        value={manualCustomer}
                        onChange={(e) => setManualCustomer(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="manual-method">Payment Method</Label>
                      <Select value={manualMethod} onValueChange={setManualMethod}>
                        <SelectTrigger id="manual-method">
                        <SelectValue placeholder="Select method">{manualMethod}</SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Cash">Cash</SelectItem>
                          <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                          <SelectItem value="Cheque">Cheque</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="manual-notes">Notes (Optional)</Label>
                      <Textarea
                        id="manual-notes"
                        value={manualNotes}
                        onChange={(e) => setManualNotes(e.target.value)}
                      />
                    </div>
                    <Button onClick={handleManualPayment}>Record Manual Payment</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="mpesa">
              {/* Mpesa STK Push Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Mpesa STK Push Request</CardTitle>
                  <CardDescription>Request payment via Mpesa STK Push.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="mpesa-phone">Customer Phone Number</Label>
                      <Input
                        id="mpesa-phone"
                        type="tel"
                        value={mpesaPhone}
                        onChange={(e) => setMpesaPhone(e.target.value)}
                        placeholder="e.g., 254712345678"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="mpesa-amount">Amount</Label>
                      <Input
                        id="mpesa-amount"
                        type="number"
                        value={mpesaAmount}
                        onChange={(e) => setMpesaAmount(e.target.value)}
                      />
                    </div>
                    <Button onClick={handleMpesaSTKPush}>Request STK Push</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Payment History Table */}
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>View past payment requests and their status.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Amount</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paymentHistory.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.amount}</TableCell>
                      <TableCell>{payment.customer}</TableCell>
                      <TableCell>{payment.method}</TableCell>
                      <TableCell>{payment.status}</TableCell>
                      <TableCell>{payment.date}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
}
