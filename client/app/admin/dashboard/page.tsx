"use client"

import { AdminLayout } from "@/components/layouts/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  ArrowDown, 
  ArrowUp, 
  CreditCard, 
  DollarSign, 
  Package, 
  ShoppingCart, 
  Users 
} from "lucide-react"
import { BarChart, Bar, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Sample data for charts
const revenueData = [
  { name: "Jan", total: 4000 },
  { name: "Feb", total: 3000 },
  { name: "Mar", total: 2000 },
  { name: "Apr", total: 2780 },
  { name: "May", total: 1890 },
  { name: "<PERSON>", total: 2390 },
  { name: "<PERSON>", total: 3490 },
  { name: "Aug", total: 2000 },
  { name: "Sep", total: 2780 },
  { name: "Oct", total: 1890 },
  { name: "Nov", total: 2390 },
  { name: "Dec", total: 3490 },
]

// Sample data for recent orders
const recentOrders = [
  { 
    id: "ORD-001", 
    customer: "John Smith", 
    email: "<EMAIL>", 
    amount: 250.00, 
    status: "completed", 
    date: "2023-07-15" 
  },
  { 
    id: "ORD-002", 
    customer: "Sarah Johnson", 
    email: "<EMAIL>", 
    amount: 150.00, 
    status: "processing", 
    date: "2023-07-14" 
  },
  { 
    id: "ORD-003", 
    customer: "Michael Brown", 
    email: "<EMAIL>", 
    amount: 350.00, 
    status: "pending", 
    date: "2023-07-13" 
  },
  { 
    id: "ORD-004", 
    customer: "Emily Davis", 
    email: "<EMAIL>", 
    amount: 120.00, 
    status: "completed", 
    date: "2023-07-12" 
  },
]

export default function Dashboard() {
  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Revenue Card */}
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$45,231.89</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span className="flex items-center text-success">
                  <ArrowUp className="mr-1 h-3 w-3" />
                  20.1%
                </span>
                <span>from last month</span>
              </div>
            </CardContent>
          </Card>
          
          {/* Orders Card */}
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Orders</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+573</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span className="flex items-center text-success">
                  <ArrowUp className="mr-1 h-3 w-3" />
                  12.2%
                </span>
                <span>from last month</span>
              </div>
            </CardContent>
          </Card>
          
          {/* Products Card */}
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2,350</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span className="flex items-center text-success">
                  <ArrowUp className="mr-1 h-3 w-3" />
                  8.4%
                </span>
                <span>from last month</span>
              </div>
            </CardContent>
          </Card>
          
          {/* Customers Card */}
          <Card className="hover-lift">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+2,873</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span className="flex items-center text-error">
                  <ArrowDown className="mr-1 h-3 w-3" />
                  3.2%
                </span>
                <span>from last month</span>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Charts and Tables */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              {/* Revenue Chart */}
              <Card className="lg:col-span-4 hover-lift">
                <CardHeader>
                  <CardTitle>Revenue</CardTitle>
                  <CardDescription>Monthly revenue for the current year</CardDescription>
                </CardHeader>
                <CardContent className="pl-2">
                  <ResponsiveContainer width="100%" height={350}>
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="name"
                        stroke="#888888"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        stroke="#888888"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `$${value}`}
                      />
                      <Tooltip 
                        formatter={(value) => [`$${value}`, 'Revenue']}
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.8)',
                          borderRadius: '8px',
                          border: '1px solid #e5e7eb',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Bar 
                        dataKey="total" 
                        fill="rgba(51, 153, 255, 0.8)" 
                        radius={[4, 4, 0, 0]} 
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              {/* Recent Orders */}
              <Card className="lg:col-span-3 hover-lift">
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                  <CardDescription>Latest customer orders</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentOrders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between space-x-4">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {order.customer.split(' ').map(name => name[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">{order.customer}</p>
                            <p className="text-xs text-muted-foreground">{order.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant={
                              order.status === 'completed' ? 'success' : 
                              order.status === 'processing' ? 'warning' : 
                              'default'
                            }
                            className="capitalize"
                          >
                            {order.status}
                          </Badge>
                          <span className="text-sm font-medium">${order.amount.toFixed(2)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-center">
                    <Button variant="outline" size="sm">View All Orders</Button>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Recent Activity */}
            <Card className="hover-lift">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest system activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex items-start space-x-4">
                      <div className="relative mt-0.5">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                          {i % 3 === 0 ? (
                            <Package className="h-4 w-4 text-primary" />
                          ) : i % 3 === 1 ? (
                            <CreditCard className="h-4 w-4 text-primary" />
                          ) : (
                            <Users className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        <span className="absolute right-0 top-0 flex h-2 w-2 rounded-full bg-success">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-success opacity-75"></span>
                        </span>
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium">
                          {i % 3 === 0
                            ? "New product added"
                            : i % 3 === 1
                            ? "Payment received"
                            : "New user registered"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {i % 3 === 0
                            ? "Premium Business Cards added to inventory"
                            : i % 3 === 1
                            ? "Payment of $199.00 received from Customer #1234"
                            : "John Smith created a new account"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {i * 10} minutes ago
                        </p>
                      </div>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <span className="sr-only">Dismiss</span>
                        <span className="h-4 w-4">×</span>
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
                <CardDescription>Detailed analytics will be displayed here</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex h-[350px] items-center justify-center border rounded-md">
                  <p className="text-muted-foreground">Analytics content coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Reports</CardTitle>
                <CardDescription>Generated reports will be displayed here</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex h-[350px] items-center justify-center border rounded-md">
                  <p className="text-muted-foreground">Reports content coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>System notifications will be displayed here</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex h-[350px] items-center justify-center border rounded-md">
                  <p className="text-muted-foreground">Notifications content coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}