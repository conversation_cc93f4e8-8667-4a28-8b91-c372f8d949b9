"use client"

import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetTrigger, SheetFooter } from "@/components/ui/sheet"
import { PlusCircle } from "lucide-react"
import toast from "react-hot-toast";
import React, { useState } from "react"

// Dummy data for users
const initialUsers = [
  {
    id: "1",
    name: "Admin User",
    email: "<EMAIL>",
    role: "Admin",
    dateRegistered: "2023-01-01",
  },
  {
    id: "2",
    name: "Customer One",
    email: "<EMAIL>",
    role: "Customer",
    dateRegistered: "2023-01-10",
  },
  {
    id: "3",
    name: "Employee A",
    email: "<EMAIL>",
    role: "Employee",
    dateRegistered: "2023-02-15",
  },
];

export default function UsersPage() {
  const [users, setUsers] = useState(initialUsers);
  const [isAddEditSheetOpen, setIsAddEditSheetOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<any | null>(null);
  const [userName, setUserName] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [userRole, setUserRole] = useState("");
  const [userPassword, setUserPassword] = useState("");

  const handleAddUser = () => {
    if (userName && userEmail && userRole && userPassword) {
      const newUser = {
        id: String(users.length + 1),
        name: userName,
        email: userEmail,
        role: userRole,
        dateRegistered: new Date().toISOString().split('T')[0],
      };
      setUsers([...users, newUser]);
      setIsAddEditSheetOpen(false);
      setUserName("");
      setUserEmail("");
      setUserRole("");
      setUserPassword("");
      toast.success("User added successfully!");
    } else {
      alert("Please fill in all fields.");
    }
  };

  const handleEditUser = () => {
    if (editingUser) {
      setUsers(users.map(user => user.id === editingUser.id ? editingUser : user));
      setIsAddEditSheetOpen(false);
      setEditingUser(null);
      setUserName("");
      setUserEmail("");
      setUserRole("");
      setUserPassword("");
      toast.success("User updated successfully!");
    }
  };

  const handleDeleteUser = (id: string) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      setUsers(users.filter(user => user.id !== id));
      toast.success("User deleted successfully!");
    }
  };

  const openEditSheet = (user: any) => {
    setEditingUser(user);
    setUserName(user.name);
    setUserEmail(user.email);
    setUserRole(user.role);
    setIsAddEditSheetOpen(true);
  };

  const openAddSheet = () => {
    setEditingUser(null);
    setUserName("");
    setUserEmail("");
    setUserRole("");
    setUserPassword("");
    setIsAddEditSheetOpen(true);
  };

  return (
    <div className="min-h-screen md:pl-64 lg:pl-72">
      <Sidebar />
      <div className="flex flex-col">
        <Header />
        <main className="grid flex-1 items-start gap-4 p-4 sm:px-6 sm:pt-4 md:gap-4">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold md:text-2xl">Users</h1>
            <div className="ml-auto flex items-center gap-2">
              <Sheet open={isAddEditSheetOpen} onOpenChange={setIsAddEditSheetOpen}>
                <SheetTrigger asChild>
                  <Button size="sm" className="h-8 gap-1" onClick={openAddSheet}>
                    <PlusCircle className="h-3.5 w-3.5" />
                    <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                      Add User
                    </span>
                  </Button>
                </SheetTrigger>
                <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>{editingUser ? "Edit User" : "Add New User"}</SheetTitle>
                    <SheetDescription>
                      {editingUser ? "Make changes to the user details." : "Add a new user to the system."}
                    </SheetDescription>
                  </SheetHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-3">
                      <Label htmlFor="user-name">Name</Label>
                      <Input
                        id="user-name"
                        type="text"
                        value={userName}
                        onChange={(e) => setUserName(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="user-email">Email</Label>
                      <Input
                        id="user-email"
                        type="email"
                        value={userEmail}
                        onChange={(e) => setUserEmail(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="user-role">Role</Label>
                      <Select value={userRole} onValueChange={(value) => {
                          setUserRole(value);
                          setEditingUser({ ...editingUser, role: value });
                        }}>
                        <SelectTrigger id="user-role" aria-label="Select role">
                          <SelectValue placeholder="Select role">{userRole}</SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Admin">Admin</SelectItem>
                          <SelectItem value="Customer">Customer</SelectItem>
                          <SelectItem value="Employee">Employee</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {!editingUser && (
                      <div className="grid gap-3">
                        <Label htmlFor="user-password">Password</Label>
                        <Input
                          id="user-password"
                          type="password"
                          value={userPassword}
                          onChange={(e) => setUserPassword(e.target.value)}
                        />
                      </div>
                    )}
                  </div>
                  <SheetFooter>
                    <Button type="submit" onClick={editingUser ? handleEditUser : handleAddUser}>
                      {editingUser ? "Save Changes" : "Add User"}
                    </Button>
                  </SheetFooter>
                </SheetContent>
              </Sheet>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                Manage your system users.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="hidden md:table-cell">Date Registered</TableHead>
                    <TableHead>
                      <span className="sr-only">Actions</span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.role}</TableCell>
                      <TableCell className="hidden md:table-cell">{user.dateRegistered}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" onClick={() => openEditSheet(user)}>Edit</Button>
                        <Button variant="destructive" size="sm" className="ml-2" onClick={() => handleDeleteUser(user.id)}>Delete</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
}