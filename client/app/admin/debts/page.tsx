"use client"

import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetDescription, SheetTrigger, SheetFooter } from "@/components/ui/sheet"
import toast from "react-hot-toast";
import { PlusCircle } from "lucide-react"
import React, { useState } from "react"

// Dummy data for debts
const initialDebts = [
  {
    id: "1",
    customerName: "Alice Johnson",
    amountOwed: "$250.00",
    dueDate: "2024-07-20",
    isPaid: false,
  },
  {
    id: "2",
    customerName: "<PERSON>",
    amountOwed: "$120.50",
    dueDate: "2024-07-15",
    isPaid: false,
  },
  {
    id: "3",
    customerName: "<PERSON> Brown",
    amountOwed: "$500.00",
    dueDate: "2024-07-25",
    isPaid: true,
  },
];

export default function DebtsPage() {
  const [debts, setDebts] = useState(initialDebts);
  const [isAddDebtSheetOpen, setIsAddDebtSheetOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState("");
  const [newAmountOwed, setNewAmountOwed] = useState("");
  const [newDueDate, setNewDueDate] = useState("");

  const handleMarkAsPaid = (id: string) => {
    setDebts(debts.map(debt =>
      debt.id === id ? { ...debt, isPaid: true } : debt
    ));
    toast.success("Debt marked as paid!");
  };

  const handleSendReminder = (customerName: string) => {
    alert(`Sending reminder to ${customerName}... (functionality to be implemented later)`);
    console.log(`Reminder sent to ${customerName}`);
    toast.success(`Reminder sent to ${customerName}!`);
  };

  const handleAddDebt = () => {
    if (newCustomerName && newAmountOwed && newDueDate) {
      const newDebt = {
        id: String(debts.length + 1),
        customerName: newCustomerName,
        amountOwed: newAmountOwed,
        dueDate: newDueDate,
        isPaid: false,
      };
      setDebts([...debts, newDebt]);
      setIsAddDebtSheetOpen(false);
      setNewCustomerName("");
      setNewAmountOwed("");
      setNewDueDate("");
      toast.success("New debt added!");
    } else {
      alert("Please fill in all fields for the new debt.");
    }
  };

  return (
    <div className="min-h-screen md:pl-64 lg:pl-72">
      <Sidebar />
      <div className="flex flex-col">
        <Header />
        <main className="grid flex-1 items-start gap-4 p-4 sm:px-6 sm:pt-4 md:gap-4">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold md:text-2xl">Debts</h1>
            <div className="ml-auto flex items-center gap-2">
              <Sheet open={isAddDebtSheetOpen} onOpenChange={setIsAddDebtSheetOpen}>
                <SheetTrigger asChild>
                  <Button size="sm" className="h-8 gap-1">
                    <PlusCircle className="h-3.5 w-3.5" />
                    <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                      Add Manual Debt
                    </span>
                  </Button>
                </SheetTrigger>
                <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Add New Debt</SheetTitle>
                    <SheetDescription>
                      Manually add a new outstanding debt.
                    </SheetDescription>
                  </SheetHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-3">
                      <Label htmlFor="customer-name">Customer Name</Label>
                      <Input
                        id="customer-name"
                        type="text"
                        value={newCustomerName}
                        onChange={(e) => setNewCustomerName(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="amount-owed">Amount Owed</Label>
                      <Input
                        id="amount-owed"
                        type="number"
                        value={newAmountOwed}
                        onChange={(e) => setNewAmountOwed(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-3">
                      <Label htmlFor="due-date">Due Date</Label>
                      <Input
                        id="due-date"
                        type="date"
                        value={newDueDate}
                        onChange={(e) => setNewDueDate(e.target.value)}
                      />
                    </div>
                  </div>
                  <SheetFooter>
                    <Button type="submit" onClick={handleAddDebt}>Add Debt</Button>
                  </SheetFooter>
                </SheetContent>
              </Sheet>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Outstanding Debts</CardTitle>
              <CardDescription>
                Track and manage customer debts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer Name</TableHead>
                    <TableHead>Amount Owed</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>
                      <span className="sr-only">Actions</span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {debts.map((debt) => (
                    <TableRow key={debt.id} className={debt.isPaid ? "bg-green-100 dark:bg-green-900" : ""}>
                      <TableCell className="font-medium">{debt.customerName}</TableCell>
                      <TableCell>{debt.amountOwed}</TableCell>
                      <TableCell>{debt.dueDate}</TableCell>
                      <TableCell>{debt.isPaid ? "Paid" : "Outstanding"}</TableCell>
                      <TableCell>
                        {!debt.isPaid && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleMarkAsPaid(debt.id)}
                          >
                            Mark as Paid
                          </Button>
                        )}
                        {!debt.isPaid && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="ml-2"
                            onClick={() => handleSendReminder(debt.customerName)}
                          >
                            Send Reminder
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
}
