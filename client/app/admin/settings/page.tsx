"use client"

import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import React, { useState } from "react"

export default function SettingsPage() {
  // General Settings State
  const [businessName, setBusinessName] = useState("Multiprints Co.");
  const [contactEmail, setContactEmail] = useState("<EMAIL>");
  const [currencySymbol, setCurrencySymbol] = useState("$");

  // Profile Settings State
  const [adminName, setAdminName] = useState("Code Goddy");
  const [adminEmail, setAdminEmail] = useState("<EMAIL>");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const handleSaveGeneralSettings = () => {
    console.log("Saving General Settings:", { businessName, contactEmail, currencySymbol });
    // Logic to save to backend
  };

  const handleSaveProfileSettings = () => {
    if (newPassword !== confirmPassword) {
      alert("New password and confirm password do not match.");
      return;
    }
    console.log("Saving Profile Settings:", { adminName, adminEmail, newPassword });
    // Logic to save to backend
    setNewPassword("");
    setConfirmPassword("");
  };

  return (
    <div className="min-h-screen w-full md:pl-64 lg:pl-72">
      <Sidebar />
      <div className="flex flex-col">
        <Header />
        <main className="grid flex-1 items-start gap-4 p-4 sm:px-6 sm:pt-4 md:gap-4">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold md:text-2xl">Settings</h1>
          </div>

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="general">General Settings</TabsTrigger>
              <TabsTrigger value="profile">Profile Settings</TabsTrigger>
            </TabsList>

            {/* General Settings Tab */}
            <TabsContent value="general">
              <Card>
                <CardHeader>
                  <CardTitle>General Application Settings</CardTitle>
                  <CardDescription>Configure basic application information.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="business-name">Business Name</Label>
                      <Input
                        id="business-name"
                        type="text"
                        value={businessName}
                        onChange={(e) => setBusinessName(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="contact-email">Contact Email</Label>
                      <Input
                        id="contact-email"
                        type="email"
                        value={contactEmail}
                        onChange={(e) => setContactEmail(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="currency-symbol">Currency Symbol</Label>
                      <Input
                        id="currency-symbol"
                        type="text"
                        value={currencySymbol}
                        onChange={(e) => setCurrencySymbol(e.target.value)}
                      />
                    </div>
                  </div>
                  <Button onClick={handleSaveGeneralSettings}>Save General Settings</Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Profile Settings Tab */}
            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle>Admin Profile Settings</CardTitle>
                  <CardDescription>Manage your personal profile details.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="admin-name">Your Name</Label>
                      <Input
                        id="admin-name"
                        type="text"
                        value={adminName}
                        onChange={(e) => setAdminName(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="admin-email">Your Email</Label>
                      <Input
                        id="admin-email"
                        type="email"
                        value={adminEmail}
                        onChange={(e) => setAdminEmail(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input
                        id="new-password"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input
                        id="confirm-password"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                      />
                    </div>
                  </div>
                  <Button onClick={handleSaveProfileSettings}>Save Profile Settings</Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
}
