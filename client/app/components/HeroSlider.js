'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ArrowRight, Sparkles, Zap, Award } from 'lucide-react';

export default function HeroSlider() {
  const heroContent = [
    {
      src: '/hero-print-1.jpg',
      alt: 'Modern printing press in action',
      badge: 'Premium Quality',
      heading: 'Transform Your Ideas Into Reality',
      subheading: 'Professional printing services that bring your vision to life with unmatched precision and quality.',
      features: ['Same-day delivery', '100% satisfaction guarantee', 'Professional design support'],
      cta: 'Start Your Project',
      ctaSecondary: 'View Portfolio'
    },
    {
      src: '/hero-print-2.jpg',
      alt: 'Creative design studio with printing samples',
      badge: 'Expert Design',
      heading: 'Where Creativity Meets Precision',
      subheading: 'From business cards to large format prints, we deliver exceptional results that make your brand stand out.',
      features: ['Custom design services', 'Fast turnaround times', 'Competitive pricing'],
      cta: 'Get Quote',
      ctaSecondary: 'Learn More'
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === images.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  // Auto-play functionality
  useEffect(() => {
    const timer = setTimeout(() => {
      goToNext();
    }, 7000); // Change content every 7 seconds
    return () => clearTimeout(timer);
  }, [currentIndex]);

  const currentContent = heroContent[currentIndex];

  return (
    <section className="relative w-full min-h-[700px] md:min-h-[800px] overflow-hidden group bg-gradient-to-br from-background via-primary-light/5 to-secondary/10">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <Image
          src={currentContent.src}
          alt={currentContent.alt}
          layout="fill"
          objectFit="cover"
          className="transition-all duration-1000 ease-in-out opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-background/60"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-6 py-20 md:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <div className="space-y-8 animate-fade-in-up">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary font-medium text-sm">
              <Sparkles className="h-4 w-4" />
              {currentContent.badge}
            </div>

            {/* Heading */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-foreground leading-tight text-balance">
              {currentContent.heading}
            </h1>

            {/* Subheading */}
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-xl">
              {currentContent.subheading}
            </p>

            {/* Features */}
            <div className="space-y-3">
              {currentContent.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 text-foreground">
                  <div className="flex-shrink-0 w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-primary"></div>
                  </div>
                  <span className="text-sm md:text-base">{feature}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button asChild size="lg" className="gradient-primary text-white hover:opacity-90 transition-all duration-300 shadow-modern-lg hover:shadow-modern-xl group">
                <Link href="/products" className="flex items-center gap-2">
                  {currentContent.cta}
                  <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-2 hover:bg-primary/5 transition-all duration-300">
                <Link href="/about">{currentContent.ctaSecondary}</Link>
              </Button>
            </div>
          </div>

          {/* Right Column - Visual Elements */}
          <div className="relative lg:block hidden">
            <div className="relative animate-fade-in-scale animation-delay-300">
              {/* Floating Cards */}
              <div className="absolute -top-8 -left-8 w-32 h-32 bg-primary/10 rounded-2xl rotate-12 animate-float animation-delay-100"></div>
              <div className="absolute -bottom-8 -right-8 w-24 h-24 bg-secondary/20 rounded-2xl -rotate-12 animate-float animation-delay-300"></div>

              {/* Main Visual */}
              <div className="relative w-full h-96 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-3xl p-8 glass-effect">
                <div className="space-y-6">
                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-white/20 rounded-xl">
                      <div className="text-2xl font-bold text-primary">500+</div>
                      <div className="text-sm text-muted-foreground">Happy Clients</div>
                    </div>
                    <div className="text-center p-4 bg-white/20 rounded-xl">
                      <div className="text-2xl font-bold text-primary">24h</div>
                      <div className="text-sm text-muted-foreground">Fast Delivery</div>
                    </div>
                  </div>

                  {/* Features Icons */}
                  <div className="flex justify-center space-x-8 pt-8">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mb-2">
                        <Zap className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs text-muted-foreground">Fast</div>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mb-2">
                        <Award className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs text-muted-foreground">Quality</div>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mb-2">
                        <Sparkles className="h-6 w-6 text-primary" />
                      </div>
                      <div className="text-xs text-muted-foreground">Creative</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Navigation */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex items-center gap-4 z-20">
        {/* Dots Navigation */}
        <div className="flex space-x-3">
          {heroContent.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`h-2 w-8 rounded-full transition-all duration-300 ${
                currentIndex === index
                  ? 'bg-primary shadow-lg'
                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
              }`}
            ></button>
          ))}
        </div>

        {/* Arrow Navigation */}
        <div className="flex gap-2 ml-4">
          <button
            onClick={goToPrevious}
            className="w-10 h-10 bg-background/80 backdrop-blur-sm border border-border rounded-full flex items-center justify-center text-foreground hover:bg-background transition-all duration-300 shadow-modern"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
          <button
            onClick={goToNext}
            className="w-10 h-10 bg-background/80 backdrop-blur-sm border border-border rounded-full flex items-center justify-center text-foreground hover:bg-background transition-all duration-300 shadow-modern"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    </section>
  );
}
