'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export default function HeroSlider() {
  const images = [
    {
      src: '/hero-print-1.jpg',
      alt: 'Modern printing press in action',
      heading: 'Precision Printing, Every Time.',
      subheading: 'Experience unparalleled quality for all your printing needs.',
    },
    {
      src: '/hero-print-2.jpg',
      alt: 'Creative design studio with printing samples',
      heading: 'Bring Your Ideas to Life.',
      subheading: 'From concept to print, we make your vision a reality.',
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === images.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  // Optional: Auto-play functionality
  useEffect(() => {
    const timer = setTimeout(() => {
      goToNext();
    }, 5000); // Change image every 5 seconds
    return () => clearTimeout(timer);
  }, [currentIndex]);

  const currentImage = images[currentIndex];

  return (
    <section className="relative w-full h-[500px] md:h-[600px] overflow-hidden group bg-background">
      <Image
        src={currentImage.src}
        alt={currentImage.alt}
        layout="fill"
        objectFit="cover"
        className="transition-opacity duration-500 ease-in-out"
      />
      <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center text-white p-4">
        <h2 className="text-3xl md:text-5xl font-bold text-center mb-4">
          {currentImage.heading}
        </h2>
        <p className="text-lg md:text-xl text-center mb-8 max-w-2xl">
          {currentImage.subheading}
        </p>
        <Button asChild className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 text-lg rounded-md">
          <Link href="/products">SHOP NOW</Link>
        </Button>
      </div>

      {/* Navigation Buttons */}
      <button
        onClick={goToPrevious}
        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-50 p-2 rounded-full text-foreground hover:bg-opacity-75 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>
      <button
        onClick={goToNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-50 p-2 rounded-full text-foreground hover:bg-opacity-75 transition-opacity duration-300 opacity-0 group-hover:opacity-100"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Dots Navigation (Optional) */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`h-2 w-2 rounded-full ${currentIndex === index ? 'bg-white' : 'bg-gray-400'} transition-colors duration-300`}
          ></button>
        ))}
      </div>
    </section>
  );
}
