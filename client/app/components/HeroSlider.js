'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ArrowRight, Sparkles, Zap, Award } from 'lucide-react';

export default function HeroSlider() {
  const heroContent = [
    {
      src: '/hero-print-1.jpg',
      alt: 'Modern printing press in action',
      badge: 'Premium Quality',
      heading: 'Transform Your Ideas Into Reality',
      subheading: 'Professional printing services that bring your vision to life with unmatched precision and quality.',
      features: ['Same-day delivery', '100% satisfaction guarantee', 'Professional design support'],
      cta: 'Start Your Project',
      ctaSecondary: 'View Portfolio'
    },
    {
      src: '/hero-print-2.jpg',
      alt: 'Creative design studio with printing samples',
      badge: 'Expert Design',
      heading: 'Where Creativity Meets Precision',
      subheading: 'From business cards to large format prints, we deliver exceptional results that make your brand stand out.',
      features: ['Custom design services', 'Fast turnaround times', 'Competitive pricing'],
      cta: 'Get Quote',
      ctaSecondary: 'Learn More'
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === images.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  // Auto-play functionality
  useEffect(() => {
    const timer = setTimeout(() => {
      goToNext();
    }, 7000); // Change content every 7 seconds
    return () => clearTimeout(timer);
  }, [currentIndex]);

  const currentContent = heroContent[currentIndex];

  return (
    <section className="relative w-full min-h-[800px] md:min-h-[900px] overflow-hidden group">
      {/* Premium Background with Gradient */}
      <div className="absolute inset-0 gradient-hero opacity-10"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background/90"></div>

      {/* Background Image with Professional Overlay */}
      <div className="absolute inset-0">
        <Image
          src={currentContent.src}
          alt={currentContent.alt}
          layout="fill"
          objectFit="cover"
          className="transition-all duration-1000 ease-in-out opacity-15"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-background/98 via-background/90 to-background/85"></div>
      </div>

      {/* Professional Pattern Overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      {/* Premium Main Content */}
      <div className="relative z-10 container mx-auto px-6 py-20 md:py-28 lg:py-40">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 items-center">
          {/* Left Column - Premium Content */}
          <div className="lg:col-span-7 space-y-10 animate-fade-in-up">
            {/* Premium Badge */}
            <div className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl bg-primary/5 border border-primary/10 text-primary font-semibold text-sm backdrop-blur-sm shadow-lg">
              <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
              <Sparkles className="h-4 w-4" />
              {currentContent.badge}
            </div>

            {/* Premium Heading */}
            <div className="space-y-6">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-foreground leading-[0.9] text-balance">
                <span className="block">Transform Your</span>
                <span className="block text-gradient-primary">Ideas Into</span>
                <span className="block">Reality</span>
              </h1>

              {/* Professional Subheading */}
              <p className="text-lg md:text-xl lg:text-2xl text-muted-foreground leading-relaxed max-w-2xl font-light">
                Professional printing services that bring your vision to life with
                <span className="text-primary font-medium"> unmatched precision</span> and
                <span className="text-accent font-medium"> exceptional quality</span>.
              </p>
            </div>

            {/* Premium Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              {[
                { icon: "⚡", title: "Same-Day", subtitle: "Delivery" },
                { icon: "✨", title: "100%", subtitle: "Satisfaction" },
                { icon: "🎨", title: "Expert", subtitle: "Design" }
              ].map((feature, index) => (
                <div key={index} className="flex items-center gap-4 p-4 rounded-2xl bg-surface/50 backdrop-blur-sm border border-border/30 hover:border-primary/20 transition-all duration-300 hover:shadow-lg">
                  <div className="flex-shrink-0 w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center text-2xl">
                    {feature.icon}
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{feature.title}</div>
                    <div className="text-sm text-muted-foreground">{feature.subtitle}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Premium CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <Button asChild size="lg" className="btn-premium px-8 py-4 text-lg font-semibold rounded-2xl shadow-xl hover:shadow-2xl group touch-target">
                <Link href="/contact" className="flex items-center justify-center gap-3">
                  <span>Get Free Quote</span>
                  <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-2 border-primary/20 hover:bg-primary/5 hover:border-primary/40 transition-all duration-300 px-8 py-4 text-lg rounded-2xl touch-target">
                <Link href="/products" className="flex items-center justify-center gap-2">
                  <span>View Portfolio</span>
                  <span className="text-primary">→</span>
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center gap-8 pt-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  {[1,2,3,4].map(i => (
                    <div key={i} className="w-8 h-8 rounded-full bg-primary/20 border-2 border-background flex items-center justify-center text-xs font-semibold text-primary">
                      {String.fromCharCode(64 + i)}
                    </div>
                  ))}
                </div>
                <span className="font-medium">500+ Happy Clients</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex text-accent">
                  {'★★★★★'.split('').map((star, i) => (
                    <span key={i} className="text-sm">{star}</span>
                  ))}
                </div>
                <span className="font-medium">4.9/5 Rating</span>
              </div>
            </div>
          </div>

          {/* Right Column - Premium Visual Elements */}
          <div className="lg:col-span-5 relative lg:block hidden">
            <div className="relative animate-fade-in-scale animation-delay-300">
              {/* Premium Floating Elements */}
              <div className="absolute -top-12 -left-12 w-40 h-40 gradient-primary rounded-3xl rotate-12 animate-float animation-delay-100 opacity-20"></div>
              <div className="absolute -bottom-12 -right-12 w-32 h-32 gradient-accent rounded-3xl -rotate-12 animate-float animation-delay-300 opacity-20"></div>
              <div className="absolute top-1/2 -right-8 w-24 h-24 bg-accent/30 rounded-2xl rotate-45 animate-float animation-delay-200"></div>

              {/* Main Premium Visual */}
              <div className="relative w-full h-[500px] card-premium p-8 rounded-3xl">
                <div className="h-full flex flex-col justify-between">
                  {/* Header */}
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-foreground mb-2">Professional Results</h3>
                    <p className="text-muted-foreground">Trusted by industry leaders</p>
                  </div>

                  {/* Premium Stats Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-6 bg-primary/5 rounded-2xl border border-primary/10">
                      <div className="text-3xl font-bold text-primary mb-1">500+</div>
                      <div className="text-sm text-muted-foreground">Projects Completed</div>
                    </div>
                    <div className="text-center p-6 bg-accent/5 rounded-2xl border border-accent/10">
                      <div className="text-3xl font-bold text-accent mb-1">24h</div>
                      <div className="text-sm text-muted-foreground">Express Delivery</div>
                    </div>
                    <div className="text-center p-6 bg-success/5 rounded-2xl border border-success/10">
                      <div className="text-3xl font-bold text-success mb-1">99%</div>
                      <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
                    </div>
                    <div className="text-center p-6 bg-info/5 rounded-2xl border border-info/10">
                      <div className="text-3xl font-bold text-info mb-1">15+</div>
                      <div className="text-sm text-muted-foreground">Years Experience</div>
                    </div>
                  </div>

                  {/* Premium Service Icons */}
                  <div className="flex justify-center space-x-6">
                    {[
                      { icon: Zap, label: "Fast", color: "text-accent" },
                      { icon: Award, label: "Premium", color: "text-primary" },
                      { icon: Sparkles, label: "Creative", color: "text-success" }
                    ].map((item, index) => (
                      <div key={index} className="text-center group">
                        <div className={`w-14 h-14 bg-surface rounded-2xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                          <item.icon className={`h-7 w-7 ${item.color}`} />
                        </div>
                        <div className="text-xs font-medium text-muted-foreground">{item.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Navigation */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex items-center gap-4 z-20">
        {/* Dots Navigation */}
        <div className="flex space-x-3">
          {heroContent.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`h-2 w-8 rounded-full transition-all duration-300 ${
                currentIndex === index
                  ? 'bg-primary shadow-lg'
                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
              }`}
            ></button>
          ))}
        </div>

        {/* Arrow Navigation */}
        <div className="flex gap-2 ml-4">
          <button
            onClick={goToPrevious}
            className="w-10 h-10 bg-background/80 backdrop-blur-sm border border-border rounded-full flex items-center justify-center text-foreground hover:bg-background transition-all duration-300 shadow-modern"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
          <button
            onClick={goToNext}
            className="w-10 h-10 bg-background/80 backdrop-blur-sm border border-border rounded-full flex items-center justify-center text-foreground hover:bg-background transition-all duration-300 shadow-modern"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    </section>
  );
}
