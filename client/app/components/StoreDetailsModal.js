'use client';

import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { MapPin, Phone, Clock, Store } from 'lucide-react';

export default function StoreDetailsModal() {
  const storeDetails = {
    name: 'Multiprints Main Store',
    phone: '+1 (234) 567-890',
    address: '123 Print Street, Printville, CA 90210',
    openHours: [
      'Monday - Friday: 9:00 AM - 6:00 PM',
      'Saturday: 10:00 AM - 4:00 PM',
      'Sunday: Closed',
    ],
    googleMapsLink: 'https://maps.app.goo.gl/your-store-location', // Replace with actual link
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="flex items-center space-x-1 cursor-pointer text-muted-foreground hover:text-foreground">
          <Store className="h-6 w-6" />
          <span className="hidden md:inline">Store</span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{storeDetails.name}</DialogTitle>
          <DialogDescription>
            Find our store details and opening hours below.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-center space-x-2">
            <Phone className="h-5 w-5 text-muted-foreground" />
            <span>{storeDetails.phone}</span>
          </div>
          <div className="flex items-start space-x-2">
            <MapPin className="h-5 w-5 text-muted-foreground mt-1" />
            <span>{storeDetails.address}</span>
          </div>
          <div className="flex items-start space-x-2">
            <Clock className="h-5 w-5 text-muted-foreground mt-1" />
            <div>
              {storeDetails.openHours.map((hour, index) => (
                <p key={index}>{hour}</p>
              ))}
            </div>
          </div>
        </div>
        <Button asChild>
          <a href={storeDetails.googleMapsLink} target="_blank" rel="noopener noreferrer">
            Get Directions
          </a>
        </Button>
      </DialogContent>
    </Dialog>
  );
}
