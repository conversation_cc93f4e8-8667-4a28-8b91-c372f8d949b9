'use client';

import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { useCart } from '@/lib/context/CartContext';
import React, { useState, useEffect } from 'react';

export default function CartIcon() {
  const { getTotalItems } = useCart();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Link href="/cart" className="relative text-muted-foreground hover:text-foreground">
      <ShoppingCart className="h-6 w-6" />
      {mounted && getTotalItems() > 0 && (
        <span className="absolute -top-2.5 -right-2.5 bg-primary text-primary-foreground rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold border-2 border-background">
          {getTotalItems()}
        </span>
      )}
    </Link>
  );
}
