'use client';

import React from 'react';
import Link from 'next/link';
import { ThemeToggle } from '@/components/theme-toggle';
import { Mail, Phone, Facebook, Twitter, Instagram, Linkedin, Search, CircleUser, Store } from 'lucide-react';
import { Input } from '@/components/ui/input';
import CartIcon from './CartIcon.js';
import StoreDetailsModal from './StoreDetailsModal.js';
import FloatingContactButton from './FloatingContactButton.js';

export default function Layout({ children }) {

  return (
    <div className="flex flex-col min-h-screen">
      {/* Top Banner */}
      <div className="w-full gradient-primary text-white text-center py-3 text-sm font-medium relative overflow-hidden">
        <div className="relative z-10">
          🚀 Order before 3pm for same day delivery! Free shipping on orders over $50
        </div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-gradient"></div>
      </div>

      {/* Main Header */}
      <header className="bg-background/95 backdrop-blur-md border-b border-border/50 sticky top-0 z-50 shadow-modern">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-modern">
                M
              </div>
              <span className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                Multiprints
              </span>
            </Link>

            {/* Search Bar */}
            <div className="relative flex-grow mx-8 max-w-md">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search products, services..."
                className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-border/50 bg-background/50 backdrop-blur-sm focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/30"
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <kbd className="px-2 py-1 text-xs bg-muted rounded text-muted-foreground">⌘K</kbd>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              <Link href="/account" className="flex items-center space-x-2 px-3 py-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-secondary/50 transition-all duration-300 group">
                <CircleUser className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                <span className="hidden md:inline font-medium">Account</span>
              </Link>
              <CartIcon />
              <ThemeToggle />
            </div>
          </div>
        </div>
        {/* Navigation Bar */}
        <nav className="bg-background/80 backdrop-blur-sm border-t border-border/30">
          <div className="container mx-auto px-6 py-3">
            <div className="flex justify-between items-center">
              <div className="flex space-x-1">
                {[
                  { href: "/", label: "Home" },
                  { href: "/products", label: "Products" },
                  { href: "/services", label: "Services" },
                  { href: "/contact", label: "Contact" },
                  { href: "/about", label: "About" }
                ].map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="px-4 py-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-secondary/50 transition-all duration-300 font-medium relative group"
                  >
                    {item.label}
                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-3/4"></div>
                  </Link>
                ))}
              </div>

              <div className="flex items-center space-x-3">
                <StoreDetailsModal />
                <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
                  <Store className="h-4 w-4" />
                  <span>Visit Store</span>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>

      <main className="flex-grow">{children}</main>

      <footer className="bg-gradient-to-br from-background via-secondary/5 to-primary/5 border-t border-border/50 pt-16 pb-8">
        <div className="container mx-auto px-6">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
            {/* Company Info */}
            <div className="space-y-6">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center text-white font-bold">
                  M
                </div>
                <h3 className="text-xl font-bold text-foreground">Multiprints</h3>
              </div>
              <p className="text-muted-foreground leading-relaxed">
                Your trusted partner for high-quality printing solutions. We bring your ideas to life with precision and creativity.
              </p>
              <div className="flex space-x-4">
                {[Facebook, Twitter, Instagram, Linkedin].map((Icon, index) => (
                  <a
                    key={index}
                    href="#"
                    className="w-10 h-10 bg-secondary/50 hover:bg-primary hover:text-white rounded-lg flex items-center justify-center text-muted-foreground transition-all duration-300 hover:scale-110 shadow-modern"
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Quick Links</h3>
              <ul className="space-y-3">
                {[
                  { href: "/", label: "Home" },
                  { href: "/about", label: "About Us" },
                  { href: "/products", label: "Products" },
                  { href: "/services", label: "Services" },
                  { href: "/contact", label: "Contact" }
                ].map((item) => (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="text-muted-foreground hover:text-primary transition-colors duration-300 flex items-center group"
                    >
                      <span className="w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-4 mr-0 group-hover:mr-2"></span>
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Services</h3>
              <ul className="space-y-3">
                {[
                  "Business Cards",
                  "Large Format Printing",
                  "Custom Apparel",
                  "Design Services",
                  "Marketing Materials"
                ].map((service) => (
                  <li key={service}>
                    <Link
                      href="/services"
                      className="text-muted-foreground hover:text-primary transition-colors duration-300 flex items-center group"
                    >
                      <span className="w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-4 mr-0 group-hover:mr-2"></span>
                      {service}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Get in Touch</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Email us</p>
                    <a href="mailto:<EMAIL>" className="text-foreground hover:text-primary transition-colors duration-300 font-medium">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Call us</p>
                    <a href="tel:+1234567890" className="text-foreground hover:text-primary transition-colors duration-300 font-medium">
                      +1 (234) 567-890
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="pt-8 border-t border-border/50">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <p className="text-sm text-muted-foreground">
                &copy; {new Date().getFullYear()} Multiprints. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <Link href="/privacy-policy" className="text-muted-foreground hover:text-primary transition-colors duration-300">
                  Privacy Policy
                </Link>
                <Link href="/terms-of-service" className="text-muted-foreground hover:text-primary transition-colors duration-300">
                  Terms of Service
                </Link>
                <Link href="/faq" className="text-muted-foreground hover:text-primary transition-colors duration-300">
                  FAQ
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
      <FloatingContactButton />
    </div>
  );
}
