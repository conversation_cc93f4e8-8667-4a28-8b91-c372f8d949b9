'use client';

import React from 'react';
import Link from 'next/link';
import { ThemeToggle } from '@/components/theme-toggle';
import { Mail, Phone, Facebook, Twitter, Instagram, Linkedin, Search, CircleUser, Store } from 'lucide-react';
import { Input } from '@/components/ui/input';
import CartIcon from './CartIcon.js';
import StoreDetailsModal from './StoreDetailsModal.js';
import FloatingContactButton from './FloatingContactButton.js';

export default function Layout({ children }) {

  return (
    <div className="flex flex-col min-h-screen">
      {/* Premium Top Banner */}
      <div className="w-full bg-primary text-primary-foreground text-center py-3 text-sm font-medium relative overflow-hidden">
        <div className="relative z-10 container mx-auto px-6">
          <div className="flex items-center justify-center gap-2">
            <span className="inline-flex items-center gap-1">
              ⚡ Same-day delivery before 3pm
            </span>
            <span className="hidden sm:inline">•</span>
            <span className="hidden sm:inline-flex items-center gap-1">
              🚚 Free shipping over $50
            </span>
            <span className="hidden md:inline">•</span>
            <span className="hidden md:inline-flex items-center gap-1">
              ✨ Professional design support
            </span>
          </div>
        </div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-gradient-shift"></div>
      </div>

      {/* Premium Main Header */}
      <header className="bg-background/98 backdrop-blur-xl border-b border-border/30 sticky top-0 z-50 shadow-lg">
        <div className="container mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            {/* Premium Logo */}
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <div className="w-12 h-12 bg-primary rounded-2xl flex items-center justify-center text-primary-foreground font-bold text-xl shadow-xl group-hover:shadow-2xl transition-all duration-300">
                  M
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-accent-foreground rounded-full"></div>
                </div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                  Multiprints
                </span>
                <span className="text-xs text-muted-foreground font-medium tracking-wide">
                  PROFESSIONAL PRINTING
                </span>
              </div>
            </Link>

            {/* Premium Search Bar */}
            <div className="relative flex-grow mx-6 lg:mx-12 max-w-lg hidden md:block">
              <div className="relative group">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors duration-300" />
                <Input
                  type="text"
                  placeholder="Search products, services, or get a quote..."
                  className="w-full pl-12 pr-16 py-4 rounded-2xl border-2 border-border/30 bg-surface/80 backdrop-blur-sm focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/40 hover:bg-surface shadow-sm focus:shadow-md text-base"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 hidden lg:flex items-center gap-2">
                  <kbd className="px-2 py-1 text-xs bg-muted/80 rounded-md text-muted-foreground border border-border/50">⌘K</kbd>
                </div>
              </div>
            </div>

            {/* Premium Actions */}
            <div className="flex items-center space-x-3">
              {/* Mobile Search Button */}
              <button className="md:hidden flex items-center justify-center w-11 h-11 rounded-xl text-muted-foreground hover:text-foreground hover:bg-secondary/60 transition-all duration-300 hover:shadow-md">
                <Search className="h-5 w-5" />
              </button>

              {/* Account Link */}
              <Link href="/account" className="flex items-center space-x-2 px-3 py-2.5 rounded-xl text-muted-foreground hover:text-foreground hover:bg-secondary/60 transition-all duration-300 group hover:shadow-md">
                <CircleUser className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                <span className="hidden lg:inline font-medium">Account</span>
              </Link>

              {/* Cart */}
              <div className="relative">
                <CartIcon />
              </div>

              {/* Get Quote Button */}
              <Button asChild className="hidden lg:flex bg-accent hover:bg-accent-hover text-accent-foreground font-semibold px-6 py-2.5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                <Link href="/contact">Get Quote</Link>
              </Button>

              {/* Theme Toggle */}
              <div className="hidden sm:block">
                <ThemeToggle />
              </div>
            </div>
          </div>
        </div>
        {/* Premium Navigation Bar */}
        <nav className="bg-surface/95 backdrop-blur-xl border-t border-border/20 shadow-sm">
          <div className="container mx-auto px-6 py-4">
            <div className="flex justify-between items-center">
              {/* Professional Navigation Links */}
              <div className="flex min-w-0 space-x-2 overflow-x-auto scrollbar-hide">
                {[
                  { href: "/", label: "Home", icon: "🏠" },
                  { href: "/products", label: "Products", icon: "📦" },
                  { href: "/services", label: "Services", icon: "⚙️" },
                  { href: "/contact", label: "Contact", icon: "📞" },
                  { href: "/about", label: "About", icon: "ℹ️" }
                ].map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center gap-2 px-4 py-2.5 rounded-xl text-muted-foreground hover:text-foreground hover:bg-secondary/60 transition-all duration-300 font-medium relative group whitespace-nowrap text-sm md:text-base hover:shadow-md"
                  >
                    <span className="text-xs opacity-70 group-hover:opacity-100 transition-opacity duration-300">{item.icon}</span>
                    {item.label}
                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-0 h-1 bg-primary rounded-full transition-all duration-300 group-hover:w-3/4"></div>
                  </Link>
                ))}
              </div>

              {/* Right Side Actions */}
              <div className="flex items-center space-x-4">
                <StoreDetailsModal />

                {/* Trust Indicators */}
                <div className="hidden xl:flex items-center space-x-6 text-sm">
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span className="font-medium">Online Now</span>
                  </div>
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <Store className="h-4 w-4" />
                    <span>Visit Store</span>
                  </div>
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <span className="text-xs">⭐</span>
                    <span>4.9/5 Rating</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>

      <main className="flex-grow">{children}</main>

      <footer className="bg-gradient-to-br from-background via-secondary/5 to-primary/5 border-t border-border/50 pt-16 pb-8">
        <div className="container mx-auto px-6">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
            {/* Company Info */}
            <div className="space-y-6">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center text-white font-bold">
                  M
                </div>
                <h3 className="text-xl font-bold text-foreground">Multiprints</h3>
              </div>
              <p className="text-muted-foreground leading-relaxed">
                Your trusted partner for high-quality printing solutions. We bring your ideas to life with precision and creativity.
              </p>
              <div className="flex space-x-4">
                {[Facebook, Twitter, Instagram, Linkedin].map((Icon, index) => (
                  <a
                    key={index}
                    href="#"
                    className="w-10 h-10 bg-secondary/50 hover:bg-primary hover:text-white rounded-lg flex items-center justify-center text-muted-foreground transition-all duration-300 hover:scale-110 shadow-modern"
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Quick Links</h3>
              <ul className="space-y-3">
                {[
                  { href: "/", label: "Home" },
                  { href: "/about", label: "About Us" },
                  { href: "/products", label: "Products" },
                  { href: "/services", label: "Services" },
                  { href: "/contact", label: "Contact" }
                ].map((item) => (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="text-muted-foreground hover:text-primary transition-colors duration-300 flex items-center group"
                    >
                      <span className="w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-4 mr-0 group-hover:mr-2"></span>
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Services</h3>
              <ul className="space-y-3">
                {[
                  "Business Cards",
                  "Large Format Printing",
                  "Custom Apparel",
                  "Design Services",
                  "Marketing Materials"
                ].map((service) => (
                  <li key={service}>
                    <Link
                      href="/services"
                      className="text-muted-foreground hover:text-primary transition-colors duration-300 flex items-center group"
                    >
                      <span className="w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-4 mr-0 group-hover:mr-2"></span>
                      {service}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-semibold text-foreground mb-6">Get in Touch</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Email us</p>
                    <a href="mailto:<EMAIL>" className="text-foreground hover:text-primary transition-colors duration-300 font-medium">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Call us</p>
                    <a href="tel:+1234567890" className="text-foreground hover:text-primary transition-colors duration-300 font-medium">
                      +1 (234) 567-890
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="pt-8 border-t border-border/50">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <p className="text-sm text-muted-foreground">
                &copy; {new Date().getFullYear()} Multiprints. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <Link href="/privacy-policy" className="text-muted-foreground hover:text-primary transition-colors duration-300">
                  Privacy Policy
                </Link>
                <Link href="/terms-of-service" className="text-muted-foreground hover:text-primary transition-colors duration-300">
                  Terms of Service
                </Link>
                <Link href="/faq" className="text-muted-foreground hover:text-primary transition-colors duration-300">
                  FAQ
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
      <FloatingContactButton />
    </div>
  );
}
