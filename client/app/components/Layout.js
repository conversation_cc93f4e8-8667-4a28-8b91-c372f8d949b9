'use client';

import React from 'react';
import Link from 'next/link';
import { ThemeToggle } from '@/components/theme-toggle';
import { Mail, Phone, Facebook, Twitter, Instagram, Linkedin, Search, CircleUser, Store } from 'lucide-react';
import { Input } from '@/components/ui/input';
import CartIcon from './CartIcon.js';
import StoreDetailsModal from './StoreDetailsModal.js';
import FloatingContactButton from './FloatingContactButton.js';

export default function Layout({ children }) {

  return (
    <div className="flex flex-col min-h-screen">
      <div className="w-full bg-primary text-primary-foreground text-center py-2 text-sm font-medium">
        Order before 3pm for same day delivery!
      </div>
      <header className="bg-background border-b sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="text-2xl font-bold text-foreground">
            Multiprints
          </Link>

          {/* Search Bar */}
          <div className="relative flex-grow mx-8">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search products..."
              className="w-full pl-9 pr-3 py-2 rounded-md border border-input bg-background focus:ring-ring focus:border-primary"
            />
          </div>

          {/* Account, Cart, Theme Toggle */}
          <div className="flex items-center space-x-4">
            <Link href="/account" className="text-muted-foreground hover:text-foreground flex items-center space-x-1">
              <CircleUser className="h-6 w-6" />
              <span className="hidden md:inline">Account</span>
            </Link>
            <CartIcon />
            <ThemeToggle />
          </div>
        </div>
        {/* Secondary Navigation Bar */}
        <nav className="bg-background border-t border-b py-2">
          <div className="container mx-auto px-6 flex justify-between items-center text-base font-medium">
            <div className="flex space-x-8">
              <Link href="/" className="text-muted-foreground hover:text-foreground hover:underline">Home</Link>
              <Link href="/products" className="text-muted-foreground hover:text-foreground hover:underline">All Products</Link>
              <Link href="/services" className="text-muted-foreground hover:text-foreground hover:underline">Services</Link>
              <Link href="/contact" className="text-muted-foreground hover:text-foreground hover:underline">Contact</Link>
              <Link href="/about" className="text-muted-foreground hover:text-foreground hover:underline">About Us</Link>
            </div>
            <StoreDetailsModal />
          </div>
        </nav>
      </header>

      <main className="flex-grow">{children}</main>

      <footer className="bg-background border-t py-12">
        <div className="container mx-auto px-6 grid grid-cols-1 md:grid-cols-4 gap-8 text-muted-foreground">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">Multiprints</h3>
            <p className="text-sm mb-2">
              Your trusted partner for high-quality printing solutions.
            </p>
            <p className="text-sm">
              &copy; {new Date().getFullYear()} Multiprints. All rights reserved.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link href="/" className="hover:text-foreground transition-colors duration-200">Home</Link></li>
              <li><Link href="/about" className="hover:text-foreground transition-colors duration-200">About Us</Link></li>
              <li><Link href="/products" className="hover:text-foreground transition-colors duration-200">Products</Link></li>
              <li><Link href="/services" className="hover:text-foreground transition-colors duration-200">Services</Link></li>
              <li><Link href="/contact" className="hover:text-foreground transition-colors duration-200">Contact</Link></li>
            </ul>
          </div>

          {/* Legal & Support */}
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">Legal & Support</h3>
            <ul className="space-y-2">
              <li><Link href="/privacy-policy" className="hover:text-foreground transition-colors duration-200">Privacy Policy</Link></li>
              <li><Link href="/terms-of-service" className="hover:text-foreground transition-colors duration-200">Terms of Service</Link></li>
              <li><Link href="/faq" className="hover:text-foreground transition-colors duration-200">FAQ</Link></li>
            </ul>
          </div>

          {/* Contact Info & Socials */}
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-4">Contact Us</h3>
            <ul className="space-y-2 mb-4">
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                <a href="mailto:<EMAIL>" className="hover:text-foreground transition-colors duration-200"><EMAIL></a>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-2" />
                <a href="tel:+1234567890" className="hover:text-foreground transition-colors duration-200">+1 (234) 567-890</a>
              </li>
            </ul>
            <div className="flex space-x-4">
              <a href="#" className="hover:text-foreground transition-colors duration-200">
                <Facebook className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-foreground transition-colors duration-200">
                <Twitter className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-foreground transition-colors duration-200">
                <Instagram className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-foreground transition-colors duration-200">
                <Linkedin className="h-6 w-6" />
              </a>
            </div>
          </div>
        </div>
      </footer>
      <FloatingContactButton />
    </div>
  );
}
