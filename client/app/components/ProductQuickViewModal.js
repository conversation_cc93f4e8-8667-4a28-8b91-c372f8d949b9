'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useCart } from '@/lib/context/CartContext';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';

export default function ProductQuickViewModal({ product, children }) {
  const { addToCart } = useCart();
  const router = useRouter();

  const handleAddToCart = () => {
    addToCart(product);
    toast.success(`${product.name} added to cart!`);
  };

  const handleBuyNow = () => {
    addToCart(product);
    router.push('/checkout');
  };

  if (!product) return null; // Should not happen if used correctly

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[900px] p-0 overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-2">
          {/* Product Details (Left) */}
          <div className="p-6 md:p-8 flex flex-col justify-between">
            <div>
              <DialogHeader className="mb-4">
                <DialogTitle className="text-3xl font-bold text-foreground">{product.name}</DialogTitle>
                <DialogDescription className="text-muted-foreground">
                  {product.description}
                </DialogDescription>
              </DialogHeader>
              <div className="mb-6">
                <p className="text-2xl font-bold text-primary mb-2">{product.price}</p>
                {/* Star Ratings Placeholder */}
                <div className="text-muted-foreground text-sm">
                  <span>☆☆☆☆☆</span>
                  <span className="ml-1 text-xs">(0 Reviews)</span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-6">
                {product.details}
              </p>
            </div>
            <div className="flex flex-col space-y-3">
              <Button onClick={handleAddToCart} className="w-full">
                Add to Cart
              </Button>
              <Button onClick={handleBuyNow} className="w-full" variant="outline">
                Buy it now
              </Button>
            </div>
          </div>

          {/* Product Image (Right) */}
          <div className="relative h-64 md:h-auto bg-muted flex items-center justify-center overflow-hidden">
            <Image
              src={product.imageUrl}
              alt={product.name}
              layout="fill"
              objectFit="cover"
              className="object-center"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
