'use client';

import React from 'react';

import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';

export default function FloatingContactButton() {
  const whatsappNumber = '1234567890'; // Replace with your actual WhatsApp number
  const message = encodeURIComponent('Hello, I have a question about your services.');
  const whatsappLink = `https://wa.me/${whatsappNumber}?text=${message}`;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button asChild size="icon" className="h-14 w-14 rounded-full shadow-lg bg-success hover:bg-success/90 transition-colors duration-200">
        <a href={whatsappLink} target="_blank" rel="noopener noreferrer">
          <MessageCircle className="h-7 w-7 text-white" />
        </a>
      </Button>
    </div>
  );
}
