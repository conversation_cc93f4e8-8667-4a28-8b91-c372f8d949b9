# Multiprints Admin Dashboard

This is the admin dashboard for Multiprints, built to manage various aspects of the business, including products, services, payments, debts, and user accounts. It provides a clean, responsive, and user-friendly interface for administrative tasks.

## Technologies Used

- **Next.js**: React framework for production-ready applications.
- **React**: JavaScript library for building user interfaces.
- **Tailwind CSS**: A utility-first CSS framework for rapid UI development.
- **shadcn/ui**: Reusable UI components built with Radix UI and Tailwind CSS.
- **Lucide React**: A collection of beautiful open-source icons.
- **Recharts**: A composable charting library built on React components.
- **react-hot-toast**: A customizable toast notification library for React.

## Getting Started

Follow these steps to set up and run the project locally.

### Prerequisites

Ensure you have Node.js (version 18 or higher) and npm installed on your machine.

### Installation

1. **Navigate to the client directory**:

    ```bash
    cd Desktop/multiprints/client
    ```

2. **Install dependencies**:

    ```bash
    npm install
    ```

### Running the Development Server

To start the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

- The **main website placeholder** is accessible at: `http://localhost:3000`
- The **admin dashboard** is accessible at: `http://localhost:3000/admin/dashboard`

### Building for Production

To build the application for production:

```bash
npm run build
```

This will create an optimized build in the `.next` directory.

### Running in Production Mode

To run the built application in production mode:

```bash
npm run start
```

## Key Features

### Dashboard

- Overview of key business metrics (Total Earnings, Total Products, Active Users).
- Monthly Revenue chart with responsive design and theme-aware colors.

### Products & Services Management

- View a list of all products and printing services.
- Add new products/services via a side-drawer modal.
- Edit existing products/services via a pre-populated side-drawer modal.
- Delete products/services.
- Image upload functionality with client-side preview (Cloudinary integration is a future backend task).

### Payments Management

- Separate tabs for manual payment entry and Mpesa STK Push requests.
- Record manual payments with amount, customer, method, and notes.
- Initiate Mpesa STK Push requests (frontend placeholder).
- View a history table of past payment requests with status.

### Debts Management

- View a list of customers with outstanding debts.
- Track amount owed and due dates.
- Mark debts as paid.
- Send payment reminders (frontend placeholder).
- Manually add new debts via a side-drawer modal.

### User Management

- View a list of system users (Admin, Customer, Employee).
- Add new users via a side-drawer modal.
- Edit existing user details and roles via a pre-populated side-drawer modal.
- Delete user accounts.

### Settings

- **General Settings**: Configure business name, contact email, and currency symbol.
- **Profile Settings**: Manage admin's personal name, email, and change password.

### User Interface & Experience (UI/UX)

- **Responsive Design**: Adapts to various screen sizes (desktop, tablet, mobile).
- **Fixed Sidebar**: Navigation remains accessible while scrolling through page content.
- **Theme Toggle**: Supports light and dark modes.
- **Toast Notifications**: Provides clear and timely feedback for user actions.
- **Consistent Modals/Sheets**: Uniform experience for adding and editing data across different sections.

## Important Notes

- **Cloudinary Integration**: The image upload functionality on the Products and Services pages currently only handles client-side preview. Full integration with Cloudinary for actual image storage and retrieval will require backend development.
- **Mpesa STK Push**: The Mpesa STK Push functionality is a frontend placeholder. Actual integration with Mpesa APIs will be a backend task.
- **Dummy Data**: All data displayed in tables and forms is currently dummy data. Backend integration will be required to connect to a real database.
- **Authentication/Authorization**: User authentication and authorization are not yet implemented. All pages are currently accessible without login.
