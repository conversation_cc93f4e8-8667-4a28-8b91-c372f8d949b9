# Technology Stack

## Framework & Runtime
- **Next.js 15.3.5** - React framework with App Router
- **React 19** - UI library
- **TypeScript 5** - Type safety and development experience
- **Node.js 18+** - Runtime requirement

## Styling & UI
- **Tailwind CSS 3.4.17** - Utility-first CSS framework
- **shadcn/ui** - Reusable component library built on Radix UI
- **Radix UI** - Headless UI primitives for accessibility
- **Lucide React** - Icon library
- **next-themes** - Theme switching (light/dark mode)
- **tailwindcss-animate** - Animation utilities

## State Management & Context
- **React Context** - Cart state management
- **react-hot-toast** - Toast notifications

## Data Visualization
- **Recharts 3.1.0** - Charts and analytics dashboard

## Development Tools
- **ESLint** - Code linting with Next.js config
- **PostCSS** - CSS processing
- **Autoprefixer** - CSS vendor prefixes

## Common Commands

### Development
```bash
npm run dev          # Start development server (http://localhost:3000)
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Key URLs
- Main website: `http://localhost:3000`
- Admin dashboard: `http://localhost:3000/admin/dashboard`

## Build Configuration
- Uses App Router (not Pages Router)
- TypeScript strict mode enabled
- Tailwind configured for all app and component directories
- Custom CSS variables for theming in globals.css