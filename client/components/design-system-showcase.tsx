"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button-premium"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardOverlay } from "@/components/ui/card-premium"
import { Badge } from "@/components/ui/badge"
import { <PERSON>rkles, Star, Zap, Heart, ArrowRight, Download, Play, ShoppingCart } from "lucide-react"

export function DesignSystemShowcase() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section with Premium Design */}
      <section className="section-premium-lg bg-gradient-to-br from-primary/5 via-background to-accent/5">
        <div className="container-premium text-center">
          <div className="animate-fade-in-up">
            <h1 className="responsive-premium-heading font-bold text-gradient-primary mb-6">
              Enhanced Design System
            </h1>
            <p className="responsive-premium-text text-muted-foreground max-w-2xl mx-auto text-premium-balance mb-8">
              Experience our premium design system with enhanced components, micro-animations, 
              and professional styling that elevates your brand presence.
            </p>
            <div className="flex flex-wrap gap-4 justify-center">
              <Button variant="premium-primary" size="lg" icon={<Sparkles className="h-5 w-5" />}>
                Get Started
              </Button>
              <Button variant="premium-accent" size="lg" icon={<Play className="h-5 w-5" />}>
                Watch Demo
              </Button>
              <Button variant="outline-premium" size="lg" icon={<Download className="h-5 w-5" />}>
                Download
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Premium Button Showcase */}
      <section className="section-premium">
        <div className="container-premium">
          <div className="text-premium-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Premium Button Variants</h2>
            <p className="text-muted-foreground">
              Gradient effects, micro-animations, and professional styling
            </p>
          </div>
          
          <div className="grid-premium-auto">
            <div className="space-premium-md">
              <h3 className="text-xl font-semibold mb-4">Gradient Buttons</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="premium-primary">Primary</Button>
                <Button variant="premium-accent">Accent</Button>
                <Button variant="premium-success">Success</Button>
                <Button variant="animated-gradient">Animated</Button>
              </div>
            </div>

            <div className="space-premium-md">
              <h3 className="text-xl font-semibold mb-4">Glass Morphism</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="glass">Glass</Button>
                <Button variant="glass-primary">Glass Primary</Button>
              </div>
            </div>

            <div className="space-premium-md">
              <h3 className="text-xl font-semibold mb-4">Special Effects</h3>
              <div className="flex flex-wrap gap-4">
                <Button variant="glow-primary">Glow Primary</Button>
                <Button variant="glow-accent">Glow Accent</Button>
                <Button variant="floating" size="floating">
                  <Star className="h-6 w-6" />
                </Button>
                <Button variant="floating-accent" size="floating">
                  <Heart className="h-6 w-6" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Premium Card Showcase */}
      <section className="section-premium bg-muted/30">
        <div className="container-premium">
          <div className="text-premium-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Enhanced Card Components</h2>
            <p className="text-muted-foreground">
              Professional cards with hover effects, shadows, and premium styling
            </p>
          </div>

          <div className="grid-premium-auto">
            {/* Premium Card */}
            <Card variant="premium" className="animate-fade-in-scale animation-delay-100">
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="badge-primary">Premium</Badge>
                </div>
                <CardTitle>Premium Design</CardTitle>
                <CardDescription>
                  Professional gradient backgrounds with enhanced shadows
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Experience premium design with subtle gradients and professional hover effects.
                </p>
                <Button variant="premium-primary" size="sm" icon={<ArrowRight className="h-4 w-4" />} iconPosition="right">
                  Learn More
                </Button>
              </CardContent>
            </Card>

            {/* Interactive Card */}
            <Card variant="interactive" className="animate-fade-in-scale animation-delay-200">
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="badge-success">Interactive</Badge>
                </div>
                <CardTitle>Interactive Elements</CardTitle>
                <CardDescription>
                  Smooth animations and micro-interactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Cards that respond beautifully to user interactions with smooth transitions.
                </p>
                <Button variant="outline-accent" size="sm" icon={<Zap className="h-4 w-4" />}>
                  Try It
                </Button>
              </CardContent>
            </Card>

            {/* Product Card */}
            <Card variant="product-featured" className="animate-fade-in-scale animation-delay-300">
              <CardOverlay gradient="accent" />
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="badge-warning">Featured</Badge>
                </div>
                <CardTitle>Product Showcase</CardTitle>
                <CardDescription>
                  Perfect for highlighting products and services
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Showcase your products with professional styling and attention-grabbing effects.
                </p>
                <Button variant="premium-accent" size="sm" icon={<ShoppingCart className="h-4 w-4" />}>
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Animation Showcase */}
      <section className="section-premium">
        <div className="container-premium">
          <div className="text-premium-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Premium Animations</h2>
            <p className="text-muted-foreground">
              Smooth micro-animations that enhance user experience
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center space-premium-sm">
              <div className="w-16 h-16 bg-primary rounded-lg mx-auto mb-3 animate-float"></div>
              <p className="text-sm font-medium">Float</p>
            </div>
            <div className="text-center space-premium-sm">
              <div className="w-16 h-16 bg-accent rounded-lg mx-auto mb-3 animate-pulse-glow"></div>
              <p className="text-sm font-medium">Pulse Glow</p>
            </div>
            <div className="text-center space-premium-sm">
              <div className="w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-lg mx-auto mb-3 animate-gradient-x"></div>
              <p className="text-sm font-medium">Gradient</p>
            </div>
            <div className="text-center space-premium-sm">
              <div className="w-16 h-16 bg-success rounded-lg mx-auto mb-3 animate-bounce-subtle"></div>
              <p className="text-sm font-medium">Bounce</p>
            </div>
          </div>
        </div>
      </section>

      {/* Utility Classes Showcase */}
      <section className="section-premium bg-muted/30">
        <div className="container-premium">
          <div className="text-premium-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Premium Utilities</h2>
            <p className="text-muted-foreground">
              Consistent spacing, layouts, and professional styling utilities
            </p>
          </div>

          <div className="grid-premium-featured">
            <div className="space-premium-lg">
              <h3 className="text-xl font-semibold mb-4">Spacing System</h3>
              <div className="space-premium-md">
                <div className="p-4 bg-primary/10 rounded-lg">
                  <code className="text-sm">space-premium-xs</code>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <code className="text-sm">space-premium-sm</code>
                </div>
                <div className="p-4 bg-primary/10 rounded-lg">
                  <code className="text-sm">space-premium-md</code>
                </div>
              </div>
            </div>

            <div className="space-premium-lg">
              <h3 className="text-xl font-semibold mb-4">Layout Utilities</h3>
              <div className="space-premium-sm">
                <div className="container-premium-narrow bg-accent/10 p-4 rounded-lg">
                  <code className="text-sm">container-premium-narrow</code>
                </div>
                <div className="flex-premium-between bg-primary/10 p-4 rounded-lg">
                  <span>Left</span>
                  <code className="text-sm">flex-premium-between</code>
                  <span>Right</span>
                </div>
                <div className="text-premium-center bg-success/10 p-4 rounded-lg">
                  <code className="text-sm">text-premium-center</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}