"use client"

import { <PERSON>actNode } from "react"
import { AdminSidebar } from "@/components/navigation/admin-sidebar"
import { AdminHeader } from "@/components/navigation/admin-header"

interface AdminLayoutProps {
  children: ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="admin-layout">
      <AdminSidebar />
      <div className="admin-main">
        <AdminHeader />
        <main className="admin-content">
          {children}
        </main>
      </div>
    </div>
  )
}