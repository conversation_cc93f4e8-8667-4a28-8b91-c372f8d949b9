
"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { Home, Package, Package2, Settings, Users2 } from "lucide-react"

export function Sidebar() {
  const pathname = usePathname();
  return (
    <div className="fixed inset-y-0 left-0 hidden w-64 border-r bg-muted/40 md:block lg:w-72">
      <div className="flex h-full flex-col gap-2">
        <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link href="/admin/dashboard" className="flex items-center gap-2 font-semibold">
            <Package2 className="h-6 w-6" />
            <span className="">Multiprints</span>
          </Link>
        </div>
        <div className="flex-1 overflow-y-auto">
          <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
            {/* View Public Site Link */}
            <Link
              href="/"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Home className="h-4 w-4" />
              View Public Site
            </Link>
            {/* Dashboard Link */}
            <Link
              href="/admin/dashboard"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/dashboard"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Home className="h-4 w-4" />
              Dashboard
            </Link>
            {/* Products Link */}
            <Link
              href="/admin/products"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/products"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Package className="h-4 w-4" />
              Products
            </Link>
            {/* Services Link */}
            <Link
              href="/admin/services"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/services"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Package className="h-4 w-4" />
              Services
            </Link>
            {/* Payments Link */}
            <Link
              href="/admin/payments"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/payments"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Home className="h-4 w-4" /> {/* Using Home icon as a placeholder for now */}
              Payments
            </Link>
            
            {/* Users Link */}
            <Link
              href="/admin/debts"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/debts"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Users2 className="h-4 w-4" /> {/* Using Users2 icon as a placeholder for now */}
              Debts
            </Link>
            
            {/* Settings Link */}
            <Link
              href="/admin/users"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/users"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Users2 className="h-4 w-4" />
              Users
            </Link>
            {/* Settings Link */}
            <Link
              href="/admin/settings"
              className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                pathname === "/admin/settings"
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <Settings className="h-4 w-4" />
              Settings
            </Link>
          </nav>
        </div>
      </div>
    </div>
  )
}
