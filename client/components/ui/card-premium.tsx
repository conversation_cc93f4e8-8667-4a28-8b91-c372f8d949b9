import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground transition-all duration-300",
  {
    variants: {
      variant: {
        // Premium card styles
        "premium": [
          "bg-gradient-to-br from-white to-gray-50/50",
          "border-gray-200/50 shadow-premium",
          "hover:shadow-premium-hover hover:-translate-y-2",
          "hover:border-primary/20",
          "transition-all duration-500 ease-out"
        ],
        "premium-dark": [
          "bg-gradient-to-br from-gray-900 to-gray-800/50",
          "border-gray-700/50 shadow-premium",
          "hover:shadow-premium-hover hover:-translate-y-2",
          "hover:border-primary/20",
          "transition-all duration-500 ease-out"
        ],
        // Glass morphism cards
        "glass": [
          "bg-white/10 backdrop-blur-md",
          "border-white/20 shadow-glass",
          "hover:bg-white/20 hover:shadow-lg hover:-translate-y-1",
          "dark:bg-black/10 dark:border-white/10 dark:hover:bg-black/20"
        ],
        "glass-primary": [
          "bg-[#0A1628]/5 backdrop-blur-md",
          "border-[#0A1628]/10 shadow-glass",
          "hover:bg-[#0A1628]/10 hover:shadow-lg hover:-translate-y-1"
        ],
        // Interactive cards
        "interactive": [
          "bg-card shadow-md border-border",
          "hover:shadow-xl hover:-translate-y-1 hover:border-primary/30",
          "cursor-pointer transition-all duration-300",
          "active:scale-[0.98]"
        ],
        "interactive-accent": [
          "bg-gradient-to-br from-white to-accent-light/30",
          "border-accent/20 shadow-md",
          "hover:shadow-xl hover:-translate-y-1 hover:border-accent/40",
          "cursor-pointer transition-all duration-300",
          "active:scale-[0.98]"
        ],
        // Product showcase cards
        "product": [
          "bg-card shadow-lg border-border overflow-hidden",
          "hover:shadow-2xl hover:-translate-y-2",
          "hover:border-primary/20",
          "transition-all duration-500 ease-out group"
        ],
        "product-featured": [
          "bg-gradient-to-br from-white via-accent-light/20 to-primary-light/20",
          "border-2 border-accent/30 shadow-premium",
          "hover:shadow-premium-hover hover:-translate-y-3",
          "hover:border-accent/50",
          "transition-all duration-500 ease-out group",
          "relative overflow-hidden"
        ],
        // Service cards
        "service": [
          "bg-gradient-to-br from-white to-primary-light/10",
          "border-primary/10 shadow-lg",
          "hover:shadow-2xl hover:-translate-y-2",
          "hover:border-primary/30 hover:bg-gradient-to-br hover:from-white hover:to-primary-light/20",
          "transition-all duration-500 ease-out"
        ],
        // Testimonial cards
        "testimonial": [
          "bg-gradient-to-br from-white to-gray-50",
          "border-gray-200 shadow-md",
          "hover:shadow-lg hover:-translate-y-1",
          "relative overflow-hidden",
          "before:absolute before:top-0 before:left-0 before:w-full before:h-1",
          "before:bg-gradient-to-r before:from-primary before:to-accent"
        ],
        // Feature cards
        "feature": [
          "bg-card shadow-md border-border",
          "hover:shadow-xl hover:-translate-y-1",
          "hover:border-primary/20",
          "transition-all duration-300 group"
        ],
        "feature-highlighted": [
          "bg-gradient-to-br from-primary-light/5 to-accent-light/5",
          "border-primary/20 shadow-lg",
          "hover:shadow-xl hover:-translate-y-2",
          "hover:border-primary/40",
          "transition-all duration-300 group"
        ],
        // Pricing cards
        "pricing": [
          "bg-card shadow-lg border-border",
          "hover:shadow-xl hover:-translate-y-2",
          "hover:border-primary/30",
          "transition-all duration-500 ease-out"
        ],
        "pricing-featured": [
          "bg-gradient-to-br from-primary to-primary-hover",
          "text-primary-foreground shadow-premium",
          "hover:shadow-premium-hover hover:-translate-y-3",
          "border-primary/50",
          "transition-all duration-500 ease-out",
          "relative overflow-hidden"
        ],
        // Default variants
        "default": "shadow-sm",
        "elevated": "shadow-md hover:shadow-lg hover:-translate-y-0.5 transition-all duration-200"
      },
      padding: {
        none: "p-0",
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
        xl: "p-10"
      }
    },
    defaultVariants: {
      variant: "default",
      padding: "default"
    }
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, asChild = false, ...props }, ref) => {
    const Comp = asChild ? "div" : "div"
    
    return (
      <Comp
        ref={ref}
        className={cn(cardVariants({ variant, padding, className }))}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

// Premium card overlay component for featured content
const CardOverlay = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    gradient?: "primary" | "accent" | "success" | "custom"
  }
>(({ className, gradient = "primary", ...props }, ref) => {
  const gradientClasses = {
    primary: "bg-gradient-to-t from-primary/80 to-transparent",
    accent: "bg-gradient-to-t from-accent/80 to-transparent", 
    success: "bg-gradient-to-t from-success/80 to-transparent",
    custom: ""
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",
        gradientClasses[gradient],
        className
      )}
      {...props}
    />
  )
})
CardOverlay.displayName = "CardOverlay"

export { 
  Card, 
  CardHeader, 
  CardFooter, 
  CardTitle, 
  CardDescription, 
  CardContent,
  CardOverlay,
  cardVariants 
}