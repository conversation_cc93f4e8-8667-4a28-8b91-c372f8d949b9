import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group",
  {
    variants: {
      variant: {
        // Premium gradient buttons
        "premium-primary": [
          "bg-gradient-to-r from-[#0A1628] to-[#081220]",
          "text-white shadow-button",
          "hover:shadow-button-hover hover:-translate-y-0.5",
          "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent",
          "before:translate-x-[-100%] before:transition-transform before:duration-500",
          "hover:before:translate-x-[100%]",
          "active:scale-95"
        ],
        "premium-accent": [
          "bg-gradient-to-r from-[#FFC333] to-[#E6AB25]",
          "text-[#0A1628] shadow-button font-semibold",
          "hover:shadow-button-hover hover:-translate-y-0.5",
          "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent",
          "before:translate-x-[-100%] before:transition-transform before:duration-500",
          "hover:before:translate-x-[100%]",
          "active:scale-95"
        ],
        "premium-success": [
          "bg-gradient-to-r from-[#2ECF2A] to-[#25B924]",
          "text-white shadow-button",
          "hover:shadow-button-hover hover:-translate-y-0.5",
          "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent",
          "before:translate-x-[-100%] before:transition-transform before:duration-500",
          "hover:before:translate-x-[100%]",
          "active:scale-95"
        ],
        // Glass morphism buttons
        "glass": [
          "bg-white/10 backdrop-blur-md border border-white/20",
          "text-foreground shadow-glass",
          "hover:bg-white/20 hover:shadow-lg hover:-translate-y-0.5",
          "dark:bg-black/10 dark:border-white/10 dark:hover:bg-black/20"
        ],
        "glass-primary": [
          "bg-[#0A1628]/10 backdrop-blur-md border border-[#0A1628]/20",
          "text-[#0A1628] shadow-glass",
          "hover:bg-[#0A1628]/20 hover:shadow-lg hover:-translate-y-0.5"
        ],
        // Outline with premium effects
        "outline-premium": [
          "border-2 border-[#0A1628] bg-transparent",
          "text-[#0A1628] shadow-sm",
          "hover:bg-[#0A1628] hover:text-white hover:shadow-button-hover hover:-translate-y-0.5",
          "transition-all duration-300"
        ],
        "outline-accent": [
          "border-2 border-[#FFC333] bg-transparent",
          "text-[#FFC333] shadow-sm",
          "hover:bg-[#FFC333] hover:text-[#0A1628] hover:shadow-button-hover hover:-translate-y-0.5",
          "transition-all duration-300"
        ],
        // Floating action buttons
        "floating": [
          "rounded-full shadow-premium bg-gradient-to-r from-[#0A1628] to-[#081220]",
          "text-white hover:shadow-premium-hover",
          "hover:scale-110 hover:-translate-y-1",
          "transition-all duration-300 ease-out"
        ],
        "floating-accent": [
          "rounded-full shadow-premium bg-gradient-to-r from-[#FFC333] to-[#E6AB25]",
          "text-[#0A1628] hover:shadow-premium-hover",
          "hover:scale-110 hover:-translate-y-1",
          "transition-all duration-300 ease-out"
        ],
        // Glow effects
        "glow-primary": [
          "bg-gradient-to-r from-[#0A1628] to-[#081220]",
          "text-white shadow-glow-primary",
          "hover:shadow-[0_0_30px_rgba(10,22,40,0.5)] hover:-translate-y-0.5",
          "transition-all duration-300"
        ],
        "glow-accent": [
          "bg-gradient-to-r from-[#FFC333] to-[#E6AB25]",
          "text-[#0A1628] shadow-glow",
          "hover:shadow-[0_0_30px_rgba(255,195,51,0.5)] hover:-translate-y-0.5",
          "transition-all duration-300"
        ],
        // Animated gradient buttons
        "animated-gradient": [
          "bg-gradient-to-r from-[#0A1628] via-[#FFC333] to-[#0A1628]",
          "bg-size-200 text-white shadow-button",
          "hover:bg-pos-0 hover:shadow-button-hover hover:-translate-y-0.5",
          "transition-all duration-500 ease-out",
          "animate-gradient-x"
        ]
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-12 rounded-lg px-10 text-base",
        "2xl": "h-14 rounded-lg px-12 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12",
        "floating": "h-14 w-14 rounded-full",
        "floating-lg": "h-16 w-16 rounded-full"
      },
    },
    defaultVariants: {
      variant: "premium-primary",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: "left" | "right"
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading, icon, iconPosition = "left", children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const isDisabled = disabled || loading
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          </div>
        )}
        
        <div className={cn(
          "flex items-center gap-2 relative z-10",
          loading && "opacity-0"
        )}>
          {icon && iconPosition === "left" && (
            <span className="flex-shrink-0">{icon}</span>
          )}
          {children}
          {icon && iconPosition === "right" && (
            <span className="flex-shrink-0">{icon}</span>
          )}
        </div>
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }