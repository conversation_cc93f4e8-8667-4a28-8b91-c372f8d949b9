"use client"

import Link from "next/link"
import { Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Printer, Twitter } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"

export function PublicFooter() {
  return (
    <footer className="bg-muted/50">
      {/* Newsletter section */}
      <div className="border-b">
        <div className="container py-12 md:py-16">
          <div className="grid gap-8 md:grid-cols-2 md:gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-2">Stay Updated</h3>
              <p className="text-muted-foreground mb-4">
                Subscribe to our newsletter for exclusive offers, printing tips, and industry news.
              </p>
            </div>
            <div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Input 
                  type="email" 
                  placeholder="Enter your email" 
                  className="flex-1" 
                />
                <Button className="whitespace-nowrap">
                  Subscribe
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                By subscribing, you agree to our Privacy Policy. You can unsubscribe at any time.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Main footer */}
      <div className="container py-12 md:py-16">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company info */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <Printer className="h-6 w-6 text-primary" />
              <span className="font-bold text-xl">Multiprints</span>
            </div>
            <p className="text-muted-foreground mb-6">
              Professional printing services for businesses and individuals. Quality prints, fast turnaround, exceptional service.
            </p>
            <div className="flex space-x-4">
              <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
                <Facebook className="h-4 w-4" />
                <span className="sr-only">Facebook</span>
              </Button>
              <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
                <Twitter className="h-4 w-4" />
                <span className="sr-only">Twitter</span>
              </Button>
              <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
                <Instagram className="h-4 w-4" />
                <span className="sr-only">Instagram</span>
              </Button>
              <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
                <Linkedin className="h-4 w-4" />
                <span className="sr-only">LinkedIn</span>
              </Button>
            </div>
          </div>
          
          {/* Quick links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products" className="text-muted-foreground hover:text-foreground">
                  Products
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-muted-foreground hover:text-foreground">
                  Services
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-foreground">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-muted-foreground hover:text-foreground">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-muted-foreground hover:text-foreground">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-muted-foreground hover:text-foreground">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Products */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Products</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products/business-cards" className="text-muted-foreground hover:text-foreground">
                  Business Cards
                </Link>
              </li>
              <li>
                <Link href="/products/flyers-brochures" className="text-muted-foreground hover:text-foreground">
                  Flyers & Brochures
                </Link>
              </li>
              <li>
                <Link href="/products/posters" className="text-muted-foreground hover:text-foreground">
                  Posters
                </Link>
              </li>
              <li>
                <Link href="/products/banners" className="text-muted-foreground hover:text-foreground">
                  Banners
                </Link>
              </li>
              <li>
                <Link href="/products/apparel" className="text-muted-foreground hover:text-foreground">
                  Custom Apparel
                </Link>
              </li>
              <li>
                <Link href="/products/promotional" className="text-muted-foreground hover:text-foreground">
                  Promotional Items
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 text-primary shrink-0 mt-0.5" />
                <span className="text-muted-foreground">
                  123 Print Street, Design District<br />
                  Nairobi, Kenya 00100
                </span>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-primary shrink-0" />
                <a href="tel:(123)456-7890" className="text-muted-foreground hover:text-foreground">
                  (*************
                </a>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-2 text-primary shrink-0" />
                <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-foreground">
                  <EMAIL>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Bottom bar */}
      <div className="border-t">
        <div className="container py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Multiprints. All rights reserved.
            </p>
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <Link href="/privacy-policy" className="hover:text-foreground">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="hover:text-foreground">
                Terms of Service
              </Link>
              <Link href="/shipping-policy" className="hover:text-foreground">
                Shipping Policy
              </Link>
              <Link href="/refund-policy" className="hover:text-foreground">
                Refund Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}