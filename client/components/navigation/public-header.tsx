"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { 
  ChevronDown, 
  Menu, 
  Printer, 
  Search, 
  ShoppingCart, 
  User, 
  X,
  Phone,
  Mail,
  MapPin,
  Clock,
  Star,
  Award,
  Zap
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>etContent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/theme-toggle"
import { Badge } from "@/components/ui/badge"

const mainNavItems = [
  { title: "Home", href: "/" },
  { 
    title: "Products", 
    href: "/products",
    description: "Professional printing solutions for your business",
    icon: Printer,
    children: [
      { 
        title: "Business Cards", 
        href: "/products/business-cards",
        description: "Premium business cards that make lasting impressions",
        icon: Award
      },
      { 
        title: "Flyers & Brochures", 
        href: "/products/flyers-brochures",
        description: "Eye-catching marketing materials",
        icon: Star
      },
      { 
        title: "Posters", 
        href: "/products/posters",
        description: "Large format posters for maximum impact",
        icon: Zap
      },
      { 
        title: "Banners", 
        href: "/products/banners",
        description: "Durable banners for events and promotions",
        icon: Award
      },
      { 
        title: "Custom Apparel", 
        href: "/products/apparel",
        description: "Branded clothing and accessories",
        icon: Star
      },
      { 
        title: "Promotional Items", 
        href: "/products/promotional",
        description: "Custom promotional products",
        icon: Zap
      },
    ]
  },
  { 
    title: "Services", 
    href: "/services",
    description: "Complete printing and design services",
    icon: Star,
    children: [
      { 
        title: "Design Services", 
        href: "/services/design",
        description: "Professional graphic design solutions",
        icon: Award
      },
      { 
        title: "Large Format Printing", 
        href: "/services/large-format",
        description: "High-quality large format printing",
        icon: Zap
      },
      { 
        title: "Digital Printing", 
        href: "/services/digital",
        description: "Fast, high-quality digital printing",
        icon: Star
      },
      { 
        title: "Finishing Services", 
        href: "/services/finishing",
        description: "Professional finishing touches",
        icon: Award
      },
    ]
  },
  { title: "About", href: "/about" },
  { title: "Contact", href: "/contact" },
]

// Search suggestions data
const searchSuggestions = [
  { title: "Business Cards", category: "Products", href: "/products/business-cards" },
  { title: "Flyers", category: "Products", href: "/products/flyers-brochures" },
  { title: "Posters", category: "Products", href: "/products/posters" },
  { title: "Banners", category: "Products", href: "/products/banners" },
  { title: "Design Services", category: "Services", href: "/services/design" },
  { title: "Large Format", category: "Services", href: "/services/large-format" },
  { title: "Digital Printing", category: "Services", href: "/services/digital" },
  { title: "Custom Apparel", category: "Products", href: "/products/apparel" },
]

export function PublicHeader() {
  const pathname = usePathname()
  const [isScrolled, setIsScrolled] = useState(false)
  const [scrollY, setScrollY] = useState(0)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<typeof searchSuggestions>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const dropdownTimeoutRef = useRef<NodeJS.Timeout>()
  
  // Enhanced scroll effect with smooth transitions
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      setScrollY(currentScrollY)
      setIsScrolled(currentScrollY > 20)
    }
    
    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Search functionality with autocomplete
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = searchSuggestions.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setSearchResults(filtered.slice(0, 6))
    } else {
      setSearchResults([])
    }
  }, [searchQuery])

  // Close search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Handle dropdown hover with delay
  const handleDropdownEnter = (itemTitle: string) => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current)
    }
    setActiveDropdown(itemTitle)
  }

  const handleDropdownLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setActiveDropdown(null)
    }, 150)
  }

  // Calculate header opacity based on scroll
  const headerOpacity = Math.min(scrollY / 100, 0.95)
  const headerBlur = Math.min(scrollY / 20, 20)
  
  return (
    <header className={cn(
      "sticky top-0 z-50 w-full transition-all duration-500 ease-out",
      isScrolled 
        ? "glass-effect shadow-lg border-b border-white/20" 
        : "bg-transparent"
    )}
    style={{
      backgroundColor: isScrolled ? `rgba(255, 255, 255, ${headerOpacity})` : 'transparent',
      backdropFilter: isScrolled ? `blur(${headerBlur}px)` : 'none',
    }}>
      {/* Enhanced Top bar with trust indicators */}
      <div className={cn(
        "border-b transition-all duration-300",
        isScrolled ? "border-white/20" : "border-border/40"
      )}>
        <div className="container flex h-12 items-center justify-between">
          <div className="hidden lg:flex items-center space-x-6 text-xs">
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Phone className="h-3 w-3" />
              <span className="font-medium">Call us: (*************</span>
            </div>
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Mail className="h-3 w-3" />
              <span className="font-medium"><EMAIL></span>
            </div>
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span className="font-medium">Mon-Fri 8AM-6PM</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Trust indicators */}
            <div className="hidden md:flex items-center space-x-4 text-xs">
              <div className="flex items-center space-x-1 text-accent">
                <Star className="h-3 w-3 fill-current" />
                <span className="font-semibold">4.9/5 Rating</span>
              </div>
              <div className="flex items-center space-x-1 text-success">
                <Award className="h-3 w-3" />
                <span className="font-semibold">10+ Years</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Link href="/account" className="text-xs text-muted-foreground hover:text-primary transition-colors">
                My Account
              </Link>
              <Link href="/track-order" className="text-xs text-muted-foreground hover:text-primary transition-colors">
                Track Order
              </Link>
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>
      
      {/* Enhanced Main navigation */}
      <div className="container flex h-20 items-center justify-between">
        {/* Enhanced Logo */}
        <Link href="/" className="flex items-center space-x-3 group">
          <div className="relative">
            <Printer className="h-8 w-8 text-primary transition-transform group-hover:scale-110" />
            <div className="absolute inset-0 bg-primary/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
          <div className="flex flex-col">
            <span className="font-bold text-2xl text-primary">Multiprints</span>
            <span className="text-xs text-muted-foreground font-medium">Professional Printing</span>
          </div>
        </Link>
        
        {/* Enhanced Desktop navigation */}
        <nav className="hidden lg:flex items-center space-x-8">
          {mainNavItems.map((item) => (
            <div 
              key={item.title} 
              className="relative"
              onMouseEnter={() => item.children && handleDropdownEnter(item.title)}
              onMouseLeave={handleDropdownLeave}
            >
              <Link
                href={item.href}
                className={cn(
                  "text-sm font-semibold transition-all duration-200 hover:text-primary relative group py-2",
                  pathname === item.href ? "text-primary" : "text-foreground"
                )}
              >
                <span className="flex items-center gap-2">
                  {item.title}
                  {item.children && (
                    <ChevronDown className={cn(
                      "h-4 w-4 transition-transform duration-200",
                      activeDropdown === item.title && "rotate-180"
                    )} />
                  )}
                </span>
                {/* Active indicator */}
                <div className={cn(
                  "absolute bottom-0 left-0 h-0.5 bg-primary transition-all duration-200",
                  pathname === item.href ? "w-full" : "w-0 group-hover:w-full"
                )} />
              </Link>
              
              {/* Enhanced Mega Menu */}
              {item.children && activeDropdown === item.title && (
                <div className="absolute left-0 top-full z-50 mt-2 w-80 rounded-2xl border border-white/20 bg-white/95 backdrop-blur-xl shadow-2xl animate-fade-in-scale">
                  <div className="p-6">
                    {/* Menu header */}
                    <div className="mb-4 pb-4 border-b border-border/20">
                      <div className="flex items-center space-x-2">
                        {item.icon && <item.icon className="h-5 w-5 text-primary" />}
                        <h3 className="font-semibold text-lg text-primary">{item.title}</h3>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                    </div>
                    
                    {/* Menu items */}
                    <div className="grid gap-2">
                      {item.children.map((child) => (
                        <Link
                          key={child.title}
                          href={child.href}
                          className="group flex items-start space-x-3 rounded-xl px-4 py-3 hover:bg-primary/5 transition-all duration-200"
                        >
                          <div className="flex-shrink-0 mt-0.5">
                            {child.icon && <child.icon className="h-4 w-4 text-primary/60 group-hover:text-primary transition-colors" />}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm text-foreground group-hover:text-primary transition-colors">
                              {child.title}
                            </div>
                            <div className="text-xs text-muted-foreground mt-0.5 line-clamp-2">
                              {child.description}
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                    
                    {/* View all link */}
                    <div className="mt-4 pt-4 border-t border-border/20">
                      <Link
                        href={item.href}
                        className="flex items-center justify-center space-x-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                      >
                        <span>View All {item.title}</span>
                        <ChevronDown className="h-4 w-4 -rotate-90" />
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </nav>
        
        {/* Enhanced Actions */}
        <div className="flex items-center space-x-3">
          {/* Enhanced Search */}
          <div className="relative" ref={searchRef}>
            <Button 
              variant="ghost" 
              size="icon" 
              className="hidden md:flex hover:bg-primary/10 transition-colors"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
            >
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>
            
            {/* Search dropdown */}
            {isSearchOpen && (
              <div className="absolute right-0 top-full z-50 mt-2 w-80 rounded-2xl border border-white/20 bg-white/95 backdrop-blur-xl shadow-2xl animate-fade-in-scale">
                <div className="p-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search products and services..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 border-border/20 focus:border-primary/20 bg-white/50"
                      autoFocus
                    />
                  </div>
                  
                  {/* Search results */}
                  {searchResults.length > 0 && (
                    <div className="mt-4 space-y-1">
                      <div className="text-xs font-medium text-muted-foreground px-2 mb-2">
                        Search Results
                      </div>
                      {searchResults.map((result) => (
                        <Link
                          key={result.href}
                          href={result.href}
                          className="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-primary/5 transition-colors"
                          onClick={() => {
                            setIsSearchOpen(false)
                            setSearchQuery("")
                          }}
                        >
                          <span className="font-medium text-sm">{result.title}</span>
                          <Badge variant="secondary" className="text-xs">
                            {result.category}
                          </Badge>
                        </Link>
                      ))}
                    </div>
                  )}
                  
                  {/* Popular searches */}
                  {searchQuery === "" && (
                    <div className="mt-4 space-y-1">
                      <div className="text-xs font-medium text-muted-foreground px-2 mb-2">
                        Popular Searches
                      </div>
                      {searchSuggestions.slice(0, 4).map((suggestion) => (
                        <Link
                          key={suggestion.href}
                          href={suggestion.href}
                          className="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-primary/5 transition-colors"
                          onClick={() => {
                            setIsSearchOpen(false)
                            setSearchQuery("")
                          }}
                        >
                          <span className="font-medium text-sm">{suggestion.title}</span>
                          <Badge variant="secondary" className="text-xs">
                            {suggestion.category}
                          </Badge>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {/* Account */}
          <Button variant="ghost" size="icon" className="hidden md:flex hover:bg-primary/10 transition-colors">
            <User className="h-5 w-5" />
            <span className="sr-only">Account</span>
          </Button>
          
          {/* Enhanced Cart */}
          <Button variant="ghost" size="icon" className="relative hover:bg-primary/10 transition-colors group">
            <ShoppingCart className="h-5 w-5 group-hover:scale-110 transition-transform" />
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-accent text-accent-foreground animate-pulse-glow">
              3
            </Badge>
            <span className="sr-only">Cart</span>
          </Button>
          
          {/* Mobile menu button */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="lg:hidden hover:bg-primary/10 transition-colors">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[350px] sm:w-[400px] bg-white/95 backdrop-blur-xl border-white/20 mobile-menu-enter">
              <div className="flex flex-col h-full">
                {/* Mobile header */}
                <div className="flex items-center justify-between border-b border-border/20 pb-6 mobile-spacing">
                  <Link href="/" className="flex items-center space-x-2">
                    <Printer className="h-6 w-6 text-primary" />
                    <span className="font-bold text-xl">Multiprints</span>
                  </Link>
                  <Button variant="ghost" size="icon" className="touch-target" onClick={() => setIsMobileMenuOpen(false)}>
                    <X className="h-5 w-5" />
                    <span className="sr-only">Close</span>
                  </Button>
                </div>
                
                {/* Mobile search */}
                <div className="py-4 border-b border-border/20 mobile-spacing">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search products and services..."
                      className="mobile-search-input pl-10 bg-white/50 border-border/20"
                    />
                  </div>
                </div>
                
                {/* Mobile navigation */}
                <div className="py-4 flex-1 overflow-y-auto mobile-spacing">
                  <div className="space-y-6">
                    {mainNavItems.map((item, index) => (
                      <div key={item.title} className={cn("space-y-3 mobile-menu-item", `animation-delay-${(index + 1) * 100}`)}>
                        <Link
                          href={item.href}
                          className={cn(
                            "mobile-nav-item text-lg font-semibold transition-colors rounded-xl",
                            pathname === item.href ? "text-primary bg-primary/5" : "text-foreground hover:text-primary hover:bg-primary/5"
                          )}
                          onClick={() => !item.children && setIsMobileMenuOpen(false)}
                        >
                          {item.icon && <item.icon className="h-5 w-5 mr-3" />}
                          <span>{item.title}</span>
                        </Link>
                        
                        {item.children && (
                          <div className="pl-7 space-y-2 border-l-2 border-primary/20">
                            {item.children.map((child) => (
                              <Link
                                key={child.title}
                                href={child.href}
                                className="mobile-nav-item text-sm text-muted-foreground hover:text-foreground transition-colors rounded-lg"
                                onClick={() => setIsMobileMenuOpen(false)}
                              >
                                {child.icon && <child.icon className="h-4 w-4 mr-2" />}
                                <span>{child.title}</span>
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Mobile footer */}
                <div className="border-t border-border/20 pt-6 space-y-4 mobile-spacing-lg">
                  <div className="grid grid-cols-2 gap-3">
                    <Button variant="outline" className="mobile-button" asChild>
                      <Link href="/account">
                        <User className="mr-2 h-4 w-4" />
                        Account
                      </Link>
                    </Button>
                    <Button className="mobile-button bg-primary hover:bg-primary/90" asChild>
                      <Link href="/cart">
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        Cart (3)
                      </Link>
                    </Button>
                  </div>
                  
                  {/* Contact info */}
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center space-x-3 mobile-nav-item rounded-lg">
                      <Phone className="h-4 w-4 text-primary flex-shrink-0" />
                      <a href="tel:(123)456-7890" className="font-medium hover:text-primary transition-colors">
                        (*************
                      </a>
                    </div>
                    <div className="flex items-center space-x-3 mobile-nav-item rounded-lg">
                      <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                      <a href="mailto:<EMAIL>" className="font-medium hover:text-primary transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                    <div className="flex items-center space-x-3 mobile-nav-item rounded-lg">
                      <Clock className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-muted-foreground">Mon-Fri 8AM-6PM</span>
                    </div>
                  </div>
                  
                  {/* Trust indicators for mobile */}
                  <div className="flex items-center justify-center space-x-4 pt-4 border-t border-border/20">
                    <div className="flex items-center space-x-1 text-accent">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="text-sm font-semibold">4.9/5</span>
                    </div>
                    <div className="flex items-center space-x-1 text-success">
                      <Award className="h-4 w-4" />
                      <span className="text-sm font-semibold">10+ Years</span>
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}