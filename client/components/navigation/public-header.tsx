"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { 
  ChevronDown, 
  Menu, 
  Printer, 
  Search, 
  ShoppingCart, 
  User, 
  X 
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/theme-toggle"
import { Badge } from "@/components/ui/badge"

const mainNavItems = [
  { title: "Home", href: "/" },
  { 
    title: "Products", 
    href: "/products",
    children: [
      { title: "Business Cards", href: "/products/business-cards" },
      { title: "Flyers & Brochures", href: "/products/flyers-brochures" },
      { title: "Posters", href: "/products/posters" },
      { title: "Banners", href: "/products/banners" },
      { title: "Custom Apparel", href: "/products/apparel" },
      { title: "Promotional Items", href: "/products/promotional" },
    ]
  },
  { 
    title: "Services", 
    href: "/services",
    children: [
      { title: "Design Services", href: "/services/design" },
      { title: "Large Format Printing", href: "/services/large-format" },
      { title: "Digital Printing", href: "/services/digital" },
      { title: "Finishing Services", href: "/services/finishing" },
    ]
  },
  { title: "About", href: "/about" },
  { title: "Contact", href: "/contact" },
]

export function PublicHeader() {
  const pathname = usePathname()
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  
  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])
  
  return (
    <header className={cn(
      "sticky top-0 z-40 w-full transition-all duration-300",
      isScrolled 
        ? "bg-background/95 backdrop-blur-md shadow-sm" 
        : "bg-transparent"
    )}>
      {/* Top bar */}
      <div className="border-b border-border/40">
        <div className="container flex h-10 items-center justify-between">
          <div className="hidden md:flex items-center space-x-4 text-xs text-muted-foreground">
            <span>Call us: (*************</span>
            <span>Email: <EMAIL></span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/account" className="text-xs text-muted-foreground hover:text-foreground">
              My Account
            </Link>
            <Link href="/track-order" className="text-xs text-muted-foreground hover:text-foreground">
              Track Order
            </Link>
            <ThemeToggle />
          </div>
        </div>
      </div>
      
      {/* Main navigation */}
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Printer className="h-6 w-6 text-primary" />
          <span className="font-bold text-xl">Multiprints</span>
        </Link>
        
        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {mainNavItems.map((item) => (
            <div 
              key={item.title} 
              className="relative"
              onMouseEnter={() => item.children && setActiveDropdown(item.title)}
              onMouseLeave={() => setActiveDropdown(null)}
            >
              <Link
                href={item.href}
                className={cn(
                  "text-sm font-medium transition-colors hover:text-primary",
                  pathname === item.href ? "text-primary" : "text-foreground"
                )}
              >
                <span className="flex items-center gap-1">
                  {item.title}
                  {item.children && <ChevronDown className="h-4 w-4" />}
                </span>
              </Link>
              
              {/* Dropdown menu */}
              {item.children && activeDropdown === item.title && (
                <div className="absolute left-0 top-full z-50 mt-1 w-48 rounded-md border bg-card shadow-lg animate-fade-in-scale">
                  <div className="p-2">
                    {item.children.map((child) => (
                      <Link
                        key={child.title}
                        href={child.href}
                        className="block rounded-md px-3 py-2 text-sm hover:bg-muted"
                      >
                        {child.title}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </nav>
        
        {/* Actions */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Search className="h-5 w-5" />
            <span className="sr-only">Search</span>
          </Button>
          
          {/* Account */}
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <User className="h-5 w-5" />
            <span className="sr-only">Account</span>
          </Button>
          
          {/* Cart */}
          <Button variant="ghost" size="icon" className="relative">
            <ShoppingCart className="h-5 w-5" />
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
              3
            </Badge>
            <span className="sr-only">Cart</span>
          </Button>
          
          {/* Mobile menu button */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[350px]">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between border-b pb-4">
                  <Link href="/" className="flex items-center space-x-2">
                    <Printer className="h-6 w-6 text-primary" />
                    <span className="font-bold text-xl">Multiprints</span>
                  </Link>
                  <Button variant="ghost" size="icon" onClick={() => setIsMobileMenuOpen(false)}>
                    <X className="h-5 w-5" />
                    <span className="sr-only">Close</span>
                  </Button>
                </div>
                
                <div className="py-4 flex-1 overflow-y-auto">
                  <div className="space-y-4">
                    {mainNavItems.map((item) => (
                      <div key={item.title} className="space-y-2">
                        <Link
                          href={item.href}
                          className={cn(
                            "block py-2 text-base font-medium",
                            pathname === item.href ? "text-primary" : "text-foreground"
                          )}
                          onClick={() => !item.children && setIsMobileMenuOpen(false)}
                        >
                          {item.title}
                        </Link>
                        
                        {item.children && (
                          <div className="pl-4 space-y-1 border-l">
                            {item.children.map((child) => (
                              <Link
                                key={child.title}
                                href={child.href}
                                className="block py-1 text-sm text-muted-foreground hover:text-foreground"
                                onClick={() => setIsMobileMenuOpen(false)}
                              >
                                {child.title}
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/account">
                        <User className="mr-2 h-4 w-4" />
                        Account
                      </Link>
                    </Button>
                    <Button className="w-full" asChild>
                      <Link href="/cart">
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        Cart (3)
                      </Link>
                    </Button>
                  </div>
                  
                  <div className="mt-4 space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Call us:</span>
                      <a href="tel:(123)456-7890" className="font-medium">(*************</a>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Email:</span>
                      <a href="mailto:<EMAIL>" className="font-medium"><EMAIL></a>
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}