"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { 
  BarChart3, 
  Box, 
  CreditCard, 
  FileText, 
  Home, 
  LayoutDashboard, 
  LogOut, 
  Package, 
  Printer, 
  Settings, 
  ShoppingCart, 
  Users 
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { ThemeToggle } from "@/components/theme-toggle"

interface SidebarLinkProps {
  href: string
  icon: React.ReactNode
  label: string
  active?: boolean
}

function SidebarLink({ href, icon, label, active }: SidebarLinkProps) {
  return (
    <Link href={href} className="w-full">
      <Button
        variant={active ? "secondary" : "ghost"}
        size="sm"
        className={cn(
          "w-full justify-start gap-2 font-medium",
          active ? "bg-secondary/10 text-secondary hover:bg-secondary/20" : "text-muted-foreground"
        )}
      >
        {icon}
        {label}
      </Button>
    </Link>
  )
}

export function AdminSidebar() {
  const pathname = usePathname()
  
  return (
    <aside className="admin-sidebar">
      <div className="flex h-full flex-col">
        {/* Logo and branding */}
        <div className="flex h-16 items-center gap-2 border-b px-6">
          <Printer className="h-6 w-6 text-primary" />
          <span className="font-semibold text-lg">Multiprints</span>
        </div>
        
        {/* Navigation links */}
        <ScrollArea className="flex-1 px-4 py-6">
          <div className="space-y-6">
            {/* Main navigation */}
            <div className="space-y-2">
              <h3 className="px-2 text-xs font-medium uppercase tracking-wider text-muted-foreground">
                Dashboard
              </h3>
              <div className="space-y-1">
                <SidebarLink 
                  href="/admin/dashboard" 
                  icon={<LayoutDashboard className="h-4 w-4" />} 
                  label="Overview" 
                  active={pathname === "/admin/dashboard"} 
                />
                <SidebarLink 
                  href="/admin/analytics" 
                  icon={<BarChart3 className="h-4 w-4" />} 
                  label="Analytics" 
                  active={pathname === "/admin/analytics"} 
                />
              </div>
            </div>
            
            {/* Store management */}
            <div className="space-y-2">
              <h3 className="px-2 text-xs font-medium uppercase tracking-wider text-muted-foreground">
                Store
              </h3>
              <div className="space-y-1">
                <SidebarLink 
                  href="/admin/products" 
                  icon={<Package className="h-4 w-4" />} 
                  label="Products" 
                  active={pathname === "/admin/products"} 
                />
                <SidebarLink 
                  href="/admin/services" 
                  icon={<FileText className="h-4 w-4" />} 
                  label="Services" 
                  active={pathname === "/admin/services"} 
                />
                <SidebarLink 
                  href="/admin/orders" 
                  icon={<ShoppingCart className="h-4 w-4" />} 
                  label="Orders" 
                  active={pathname === "/admin/orders"} 
                />
              </div>
            </div>
            
            {/* Finance */}
            <div className="space-y-2">
              <h3 className="px-2 text-xs font-medium uppercase tracking-wider text-muted-foreground">
                Finance
              </h3>
              <div className="space-y-1">
                <SidebarLink 
                  href="/admin/payments" 
                  icon={<CreditCard className="h-4 w-4" />} 
                  label="Payments" 
                  active={pathname === "/admin/payments"} 
                />
                <SidebarLink 
                  href="/admin/debts" 
                  icon={<FileText className="h-4 w-4" />} 
                  label="Debts" 
                  active={pathname === "/admin/debts"} 
                />
              </div>
            </div>
            
            {/* Users */}
            <div className="space-y-2">
              <h3 className="px-2 text-xs font-medium uppercase tracking-wider text-muted-foreground">
                Users
              </h3>
              <div className="space-y-1">
                <SidebarLink 
                  href="/admin/users" 
                  icon={<Users className="h-4 w-4" />} 
                  label="Manage Users" 
                  active={pathname === "/admin/users"} 
                />
              </div>
            </div>
            
            {/* Settings */}
            <div className="space-y-2">
              <h3 className="px-2 text-xs font-medium uppercase tracking-wider text-muted-foreground">
                Settings
              </h3>
              <div className="space-y-1">
                <SidebarLink 
                  href="/admin/settings" 
                  icon={<Settings className="h-4 w-4" />} 
                  label="General Settings" 
                  active={pathname === "/admin/settings"} 
                />
              </div>
            </div>
          </div>
        </ScrollArea>
        
        {/* Footer actions */}
        <div className="border-t p-4">
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="sm" className="gap-2 text-muted-foreground">
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
            <ThemeToggle />
          </div>
          <Separator className="my-4" />
          <div className="flex items-center justify-between">
            <Link href="/" className="text-xs text-muted-foreground hover:text-foreground">
              <Button variant="ghost" size="sm" className="gap-2 text-muted-foreground">
                <Home className="h-4 w-4" />
                View Site
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </aside>
  )
}